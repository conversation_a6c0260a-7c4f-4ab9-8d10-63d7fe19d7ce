/**
 * EastKing - Dispatch Product Order Form Logic
 * This script handles the functionality for the dispatch product order form.
 */
document.addEventListener('DOMContentLoaded', async function () {

    // --- DOM Element Variables ---
    const orderForm = document.getElementById('dispatch-order-form');
    const companyDivisionSelect = document.getElementById('companyDivision');
    const storeSelect = document.getElementById('storeSelect');
    const promotionSelect = document.getElementById('promotionSelect');
    const addPaymentButtons = document.getElementById('add-payment-buttons');
    const paymentContainer = document.getElementById('payment-methods-container');
    const formTitle = document.getElementById('form-title');
    const pageMainTitle = document.getElementById('page-main-title');
    const searchCustomerBtn = document.getElementById('searchCustomerBtn');
    const searchProductBtn = document.getElementById('dispatch-product-search-btn');
    const searchResultsModalEl = document.getElementById('productSearchResultsModal');
    const customerSelectModalBody = document.getElementById('customerSelectModalBody');
    const tableBody = document.getElementById('order-items-table-body');
    const orderDateInput = document.getElementById('orderDate');
    const invoiceDateInput = document.getElementById('invoiceDate');
    const installationDateInput = document.getElementById('installationDate');
    const distributorSelect = document.getElementById('distributorSelect');
    const salespersonSelect = document.getElementById('salespersonSelect');
    const technicianSelect = document.getElementById('technicianId');
    const hqReturnBtn = document.getElementById('hq-return-btn');
    const hqRejectBtn = document.getElementById('hq-reject-btn');
    const hqApproveBtn = document.getElementById('hq-approve-btn');
    const resubmitForHqReviewBtn = document.getElementById('resubmit-for-hq-review-btn');
    const copyOrderBtn = document.getElementById('copy-order-btn');
    const createDispatchRepairBtn = document.getElementById('create-dispatch-repair-btn');
    const storeInvoiceAmountInput = document.getElementById('storeInvoiceAmount');
    const deleteBtn = document.getElementById('delete-btn');

    // --- Global State ---
    let isEditMode = false;
    let currentOrderId = null;
    let currentOrderData = null;
    let eventListenersAttached = false; // Flag to prevent multiple bindings
    let installationAddressComponent = null;
    let availableWarehouses = []; // 全域變數，用於暫存可用的倉庫列表
    let tabCounter = 0; // 用於生成唯一的 TAB ID
    let isSearchingForMainProduct = false;
    let currentTargetPaneId = null;
    let currentItemTypeToAdd = null; // 新增旗標，記錄要新增的附屬品類型
    let currentPromotionsData = []; // To store full promotion data
    let isFormReadOnlyForStatus30 = false; // Flag to indicate if the form is in a read-only state for status 30
    let storeInvoiceAmountManuallyChanged = false; // New flag

    // =================================================================================
    //  Helper Functions
    // =================================================================================

    // Ensure getCompanyDivisionCode function exists globally
    if (typeof window.getCompanyDivisionCode !== 'function') {
        window.getCompanyDivisionCode = function() {
            const companyContext = localStorage.getItem('selectedCompanyContext');
            if (companyContext === 'QUEYOU') return 2; //雀友
            return 1; //東方不敗
        };
    }

    // =================================================================================
    //  FUNCTION DEFINITIONS
    // =================================================================================

    /**
     * Attaches all event listeners for the form.
     */
    function attachEventListeners() {
        if (eventListenersAttached) return; // Prevent re-binding

        if (storeSelect) {
            storeSelect.addEventListener('change', handleStoreOrDistributorChange);
        }
        if (distributorSelect) {
            distributorSelect.addEventListener('change', handleStoreOrDistributorChange);
        }
        if (promotionSelect) {
            promotionSelect.addEventListener('change', applyPromotionsToItems);
        }
        if (addPaymentButtons) {
            addPaymentButtons.addEventListener('click', handleAddPaymentClick);
        }

        // 初始化日期驗證
        initializeDateValidation();
        if (paymentContainer) {
            paymentContainer.addEventListener('click', handlePaymentItemActions);
            paymentContainer.addEventListener('change', function(event) {
                if (event.target.classList.contains('card-payment-type')) {
                    const row = event.target.closest('.payment-item');
                    const installmentsInput = row.querySelector('.card-installments');
                    if (event.target.value === '2') {
                        installmentsInput.style.display = 'block';
                        installmentsInput.value = '';
                        installmentsInput.placeholder = "請輸入期數";
                    } else {
                        installmentsInput.style.display = 'none';
                        installmentsInput.value = '0'; 
                    }
                }
            });
            paymentContainer.addEventListener('input', async function(event) {
                if (event.target.classList.contains('card-number')) {
                    const cardNumberInput = event.target;
                    const infoDisplay = cardNumberInput.closest('.position-relative').querySelector('.card-info-display');
                    const cardNumber = cardNumberInput.value.replace(/\s+/g, '');

                    infoDisplay.textContent = ''; // 清空舊的查詢結果

                    if (cardNumber.length === 16) {
                        const prefix = cardNumber.substring(0, 8); // 仍然使用前8位查詢
                        infoDisplay.textContent = '查詢中...';
                        try {
                            const response = await window.fetchAuthenticated(`/api/v1/utils/bin-lookup/${prefix}`);
                            if (response.ok) {
                                const result = await response.json();
                                if(result.data) {
                                    infoDisplay.textContent = `${result.data.brand || ''} - ${result.data.issuer || ''}`;
                                } else {
                                    infoDisplay.textContent = '查無卡片資訊';
                                }
                            } else {
                                infoDisplay.textContent = '查詢失敗';
                            }
                        } catch (error) {
                            console.error('BIN lookup error:', error);
                            infoDisplay.textContent = '查詢錯誤';
                        }
                    }
                }
            });
        }
        if (orderForm) {
            orderForm.addEventListener('change', function(event) {
                if (event.target.id === 'technicianId') {
                    handleTechnicianChange();
                }
            });
        }

        // 為按鈕明確綁定點擊事件
        const saveDraftBtn = document.getElementById('save-draft-btn');
        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', (e) => {
                e.preventDefault();
                handleFormSubmit(true); // isDraft = true
            });
        }

        const submitOrderBtn = document.getElementById('submit-order-btn');
        if (submitOrderBtn) {
            submitOrderBtn.addEventListener('click', (e) => {
                e.preventDefault();
                handleFormSubmit(false); // isDraft = false
            });
        }

        const checkoutBtn = document.getElementById('checkout-btn');
        if(checkoutBtn) {
            checkoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                handleFinalCheckout();
            });
        }

        if (searchCustomerBtn) {
            searchCustomerBtn.addEventListener('click', handleCustomerSearch);
        }
        if (searchProductBtn) {
            searchProductBtn.addEventListener('click', () => {
                isSearchingForMainProduct = true;
                handleProductSearch(false);
            });
        }
        if (searchResultsModalEl) {
            searchResultsModalEl.addEventListener('click', handleProductSelection);
        }
        if (customerSelectModalBody) {
            customerSelectModalBody.addEventListener('click', handleCustomerSelectionFromModal);
        }

        const modalSearchBtn = document.getElementById('execProductSearchModalBtn');
        if(modalSearchBtn) {
            modalSearchBtn.addEventListener('click', () => handleProductSearch(true));
        }

        document.querySelectorAll('input[name="invoiceTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', () => { handleInvoiceTypeChange(); updateTotalAmountsUI(); });
        });
        document.querySelectorAll('input[name="taxTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', updateTotalAmountsUI);
        });

        // 監聽整個 table body 的事件，用於處理動態新增的元素的事件
        if (tableBody) {
            tableBody.addEventListener('change', async (event) => {
                if (event.target.classList.contains('warehouse-select')) {
                    // This listener is now redundant because of the one on tabContent.
                    // await checkStockAndWarn(event.target);
                }
            });
            tableBody.addEventListener('input', async (event) => {
                if (event.target.classList.contains('quantity-input')) {
                     const row = event.target.closest('tr');
                     const warehouseSelect = row.querySelector('.warehouse-select');
                     if (warehouseSelect && warehouseSelect.value) {
                        await checkStockAndWarn(warehouseSelect);
                     }
                }
            });
        }

        // 使用事件代理來處理動態生成的 TAB 內容中的事件
        const tabContent = document.getElementById('dispatch-group-tab-content');
        tabContent.addEventListener('click', (event) => {
            if (event.target.closest('.add-sub-item-btn')) {
                isSearchingForMainProduct = false; 
                const currentPane = event.target.closest('.tab-pane');
                currentTargetPaneId = currentPane.id;
                currentItemTypeToAdd = event.target.closest('.add-sub-item-btn').dataset.itemType; // 記錄是贈品(GIFT)還是加購品(ADDON)
                handleProductSearch(false);
            }
            if (event.target.closest('.remove-item-btn')) {
                event.target.closest('tr').remove();
                updateTotalAmountsUI();
            }
            if (event.target.closest('.awaiting-material-btn')) {
                handleAwaitingMaterialClick(event.target.closest('.awaiting-material-btn'));
            }
        });

        // This is the correct, central place to handle warehouse changes for all tabs.
        tabContent.addEventListener('change', (event) => {
            if (event.target.classList.contains('warehouse-select')) {
                // When a warehouse is selected, immediately re-validate to remove the red border
                validateAllItemStock();
            }
            
            // 處理主商品出貨方式變更
            if (event.target.classList.contains('dispatch-type-select')) {
                const currentPane = event.target.closest('.tab-pane');
                if (currentPane) {
                    // 檢查是否為主商品的出貨方式選單（在主商品資訊區塊內）
                    const isMainProductDispatchType = event.target.closest('.card-body') && 
                                                    event.target.closest('.card-body').querySelector('h5') && 
                                                    event.target.closest('.card-body').querySelector('h5').textContent.includes('主商品資訊');
                    
                    if (isMainProductDispatchType) {
                        handleMainProductShipmentMethodChange(currentPane, event.target.value);
                    }
                }
            }
        });

        // Add event delegation for quantity inputs
        tabContent.addEventListener('input', (event) => {
            if (event.target.classList.contains('quantity-input')) {
                updateTotalAmountsUI();
            }
        });

        // 使用事件代理處理 TAB 頁籤的關閉按鈕
        const tabsContainer = document.getElementById('dispatch-group-tabs');
        if (tabsContainer) {
            tabsContainer.addEventListener('click', (event) => {
                // 確保點擊的是關閉按鈕
                if (event.target.classList.contains('btn-close')) {
                    event.preventDefault();
                    event.stopPropagation();

                    // 從關閉按鈕向上找到 nav-link 按鈕，並獲取其 ID
                    const tabLink = event.target.closest('.nav-link');
                    if (tabLink && tabLink.id) {
                        handleRemoveTab(tabLink.id);
                    } else {
                        console.error("Could not find parent tab link for close button.");
                    }
                }
            });
        }

        // Add listeners for new HQ approval buttons
        if (hqReturnBtn) hqReturnBtn.addEventListener('click', () => handleHqAction('hq-return-for-correction', true)); // true for requiresReason
        if (hqRejectBtn) hqRejectBtn.addEventListener('click', () => handleHqAction('hq-reject', true));
        if (hqApproveBtn) hqApproveBtn.addEventListener('click', () => handleHqAction('hq-approve', false));
        if (resubmitForHqReviewBtn) resubmitForHqReviewBtn.addEventListener('click', () => handleHqAction('submit-for-hq-review', false));
        if (copyOrderBtn) copyOrderBtn.addEventListener('click', () => {
            if(currentOrderId) {
                window.location.href = `dispatch_product_order_form.html?copyFromId=${currentOrderId}`;
            }
        });
        if (createDispatchRepairBtn) createDispatchRepairBtn.addEventListener('click', handleCreateDispatchRepair);

        if (storeInvoiceAmountInput) {
            storeInvoiceAmountInput.addEventListener('input', () => {
                storeInvoiceAmountManuallyChanged = true;
                updateTotalAmountsUI(); // Recalculate tax immediately
            });
        }

        eventListenersAttached = true; // Set the flag
    }

    /**
     * Populates a select dropdown with options.
     * @param {HTMLElement} selectElement - The select element to populate.
     * @param {Array} items - The array of items to use for options.
     * @param {string} defaultOptionText - The text for the default, non-selectable option.
     * @param {string} valueField - The name of the field in the item object for the option's value.
     * @param {string} textField - The name of the field in the item object for the option's text.
     */
    function populateDropdown(selectElement, items, defaultOptionText, valueField, textField) {
        if (!selectElement) return;
        selectElement.innerHTML = `<option value="">${defaultOptionText}</option>`;
        if (items && items.length > 0) {
            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item[valueField];
                option.textContent = item[textField];
                selectElement.appendChild(option);
            });
        }
    }

    /**
     * Populates the 'Company' dropdown from localStorage and disables it.
     */
    async function populateCompanyDivision() {
        const companyContext = localStorage.getItem('selectedCompanyContext');
        if (companyContext && companyDivisionSelect) {
            const companyCode = window.getCompanyDivisionCode();
            const companyName = companyContext === 'QUEYOU' ? '雀友' : '東方不敗';
            companyDivisionSelect.innerHTML = `<option value="${companyCode}">${companyName}</option>`;
        } else if (companyDivisionSelect) {
            companyDivisionSelect.innerHTML = `<option value="">未知的公司別</option>`;
            if(window.showToast) showToast('無法獲取公司資訊，請重新登入。', 'danger');
        }
    }

    /**
     * Fetches the user's operable stores and populates the 'Store' dropdown.
     */
    async function loadStores() {
        if (!storeSelect) return;
        try {
            const response = await fetchAuthenticated('/api/v1/auth/operable-stores');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API response did not contain store data.");

            const stores = apiResponse.data;
            storeSelect.innerHTML = '<option value="">請選擇門市</option>';
            if (stores && stores.length > 0) {
                stores.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.storeId;
                    option.textContent = store.storeName;
                    storeSelect.appendChild(option);
                });
                // If there's only one store, auto-select it.
                if (stores.length === 1) {
                    storeSelect.value = stores[0].storeId;
                    storeSelect.dispatchEvent(new Event('change'));
                }
        } else {
                storeSelect.innerHTML = '<option value="">無可用門市</option>';
        }
    } catch (error) {
            console.error('無法載入門市列表:', error);
            if(window.showToast) showToast('無法載入門市列表', 'danger');
            storeSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fetches and populates the 'Promotions' dropdown based on the selected store or distributor.
     */
    async function loadPromotions(storeId, distributorId) {
        if (!storeId && !distributorId) {
            promotionSelect.innerHTML = '<option value="">請先選擇開單門市或銷售據點</option>';
            promotionSelect.disabled = true;
            return;
        }

        promotionSelect.innerHTML = '<option value="">載入中...</option>';
        promotionSelect.disabled = true;
        try {
            let url = `/api/v1/promotions/selectable?`;
            if (storeId) url += `storeId=${storeId}`;
            if (distributorId) url += `${storeId ? '&' : ''}distributorId=${distributorId}`;

            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const apiResponse = await response.json();
            const promotions = apiResponse.data;
            currentPromotionsData = promotions; // Store full promotion data

            promotionSelect.innerHTML = '<option value="">請選擇優惠活動</option>';
            if (promotions && promotions.length > 0) {
                promotions.forEach(promo => {
                    const option = document.createElement('option');
                    option.value = promo.promotionId;
                    option.textContent = promo.promotionName;
                    promotionSelect.appendChild(option);
                });
            } else {
                promotionSelect.innerHTML = '<option value="">無適用活動</option>';
            }
        } catch (error) {
            console.error(`無法載入優惠活動:`, error);
            promotionSelect.innerHTML = '<option value="">載入失敗</option>';
        } finally {
            promotionSelect.disabled = false;
        }
    }

    function handleAddPaymentClick(event) {
        if (event.target.matches('button[data-payment-type]')) {
            addPaymentMethodRow(event.target.dataset.paymentType);
        }
    }

    function addPaymentMethodRow(type) {
        const paymentId = `payment-${Date.now()}`;
        let contentHtml = '';

        // Correct the type mapping here to match the backend Enum
        const dataType = type.toUpperCase();
        let correctedDataType = dataType;
        if (dataType === 'TECHNICIAN_COLLECT') {
            correctedDataType = 'TECHNICIAN_COLLECTION';
        } else if (dataType === 'STAFF_COLLECT') {
            correctedDataType = 'STAFF_COLLECTION';
        }

        const baseRowStart = `<div class="payment-item row g-3 mb-2 align-items-center" id="${paymentId}" data-type="${correctedDataType}">`;
        const removeButton = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn"><i class="bi bi-trash"></i></button></div>`;
        const baseRowEnd = `</div>`;

        switch (type) {
            case 'cash':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div><div class="col-md-4"></div>`;
                break;
            case 'credit_card':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                    <div class="col-md-3 position-relative">
                        <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                        <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'bank_transfer':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'technician_collection':
                const technicianSelect = document.getElementById('technicianId');
                const selectedTechnicianName = technicianSelect.options[technicianSelect.selectedIndex]?.text || '請先指派技師';
                contentHtml = `
                    <div class="col-md-3"><input type="text" class="form-control" value="技師代收款" readonly></div>
                    <div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div>
                    <div class="col-md-4"><input type="text" class="form-control technician-name-display" value="${selectedTechnicianName}" readonly></div>`;
                break;
            case 'staff_collection':
                contentHtml = `
                    <div class="col-md-3"><input type="text" class="form-control" value="人員代收款" readonly></div>
                    <div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div>
                    <div class="col-md-4"><input type="text" class="form-control" placeholder="輸入姓名或員編"></div>`;
                break;
        }

        const paymentHtml = `${baseRowStart}${contentHtml}${removeButton}${baseRowEnd}`;
        if(paymentContainer) paymentContainer.insertAdjacentHTML('beforeend', paymentHtml);
        // Add listener to the new amount input
        const newRow = paymentContainer.querySelector(`#${paymentId}`);
        newRow.querySelector('.payment-amount')?.addEventListener('input', updateTotalAmountsUI);
        newRow.querySelector('.card-number')?.addEventListener('input', handleCardNumberInput);
    }

    /**
     * Handles the change event of the technician dropdown to update payment rows.
     */
    function handleTechnicianChange() {
        const technicianSelect = document.getElementById('technicianId');
        if (!technicianSelect) return;

        const selectedTechnicianName = technicianSelect.options[technicianSelect.selectedIndex]?.text || '請先指派技師';

        const techPaymentRows = document.querySelectorAll('.payment-item[data-type="technician_collection"]');

        techPaymentRows.forEach(row => {
            const nameInput = row.querySelector('.technician-name-display');
            if (nameInput) {
                nameInput.value = selectedTechnicianName;
            }
        });
    }

    /**
     * Handles card number input to trigger BIN lookup.
     */
    async function handleCardNumberInput(event) {
        const cardNumberInput = event.target;
        const infoDisplay = cardNumberInput.closest('.position-relative').querySelector('.card-info-display');
        const cardNumber = cardNumberInput.value.replace(/\s+/g, '');

        infoDisplay.textContent = ''; // Clear old results

        if (cardNumber.length == 16) { // Trigger lookup on 8 digits
            const prefix = cardNumber.substring(0, 8);
            infoDisplay.textContent = '查詢中...';
            try {
                const response = await window.fetchAuthenticated(`/api/v1/utils/bin-lookup/${prefix}`);
                if (response.ok) {
                    const result = await response.json();
                    if(result.data) {
                        infoDisplay.textContent = `${result.data.brand || ''} - ${result.data.issuer || ''}`;
                    } else {
                        infoDisplay.textContent = '查無卡片資訊';
                    }
                } else {
                    infoDisplay.textContent = '查詢失敗';
                }
            } catch (error) {
                console.error('BIN lookup error:', error);
                infoDisplay.textContent = '查詢錯誤';
            }
        }
    }

    /**
     * Handles clicks on remove buttons for payment items.
     */
    function handlePaymentItemActions(event) {
        if (event.target.classList.contains('remove-payment-btn')) {
            event.target.closest('.payment-item').remove();
            updateTotalAmountsUI(); // Re-calculate totals after removing an item
        }
    }

    async function handleFormSubmit(isDraft = true) {
        if(window.event) window.event.preventDefault();

        // --- 日期驗證 ---
        if (!validateInstallationDate()) {
            showToast('預計安裝日期不能選擇今天以前的日期', 'warning');
            return; // 阻止提交
        }

        // --- NEW VALIDATION: Store or Distributor must be selected ---
        const storeId = document.getElementById('storeSelect')?.value;
        const distributorId = document.getElementById('distributorSelect')?.value;

        if (!storeId && !distributorId) {
            showToast('請至少選擇「開單門市」或「銷售據點」其中一項。', 'warning');
            return; // Stop submission
        }
        // --- END OF NEW VALIDATION ---

        // --- Validation Logic ---
        const payload = buildPayload(); // Build payload early to use it in validation

        // --- NEW VALIDATION for "Submit" action ---
        if (!isDraft) {
            // 1. 檢查客戶資料
            if (!payload.memberId) {
                showToast('請先查詢並選擇客戶資料。', 'warning');
                return;
            }
            // 2. 檢查商品資料
            if (!payload.items || payload.items.length === 0) {
                showToast('請至少新增一項主商品。', 'warning');
                return;
            }

            // 3. 檢查商品是否都已指定倉庫(暫時關閉,可到門市結帳前再指定)
            // for (const item of payload.items) {
                // if (!item.warehouseCode) {
                    // showToast(`主商品「${item.productName}」尚未指定出貨倉庫。`, 'warning');
                    // return;
                // }
                // if (item.itemGroups && item.itemGroups.length > 0) {
                    // for (const group of item.itemGroups) {
                        // if (!group.warehouseId) {
                            // showToast(`附屬品「${group.productName}」尚未指定出貨倉庫。`, 'warning');
                            // return;
                        // }
                    // }
                // }
            // }

            // 4. 檢查是否已指派技師
            // 僅當不是所有主商品都是店取時才檢查技師指派
            if (!areAllMainProductsStorePickup() && !payload.technicianId) {
                showToast('尚未指派技師。', 'warning');
                return;
            }
        }
        // --- END OF NEW VALIDATION for "Submit" ---

        let paymentValidationFailed = false;
        if (!isDraft) { // 僅在送出時嚴格驗證付款
            document.querySelectorAll('#payment-methods-container .payment-item').forEach(row => {
                const amountInput = row.querySelector('.payment-amount');
                const amount = parseFloat(amountInput.value);
                if (!amount || amount <= 0) {
                    showToast('請填寫所有付款項目的金額，且金額必須大於 0。', 'warning');
                    amountInput.classList.add('is-invalid'); // Highlight the invalid input
                    paymentValidationFailed = true;
                } else {
                    amountInput.classList.remove('is-invalid');
                }
            });
        }

        if (paymentValidationFailed) {
            return; // Stop the submission
        }

        const addressData = installationAddressComponent.getData();

        if (isDraft) {
            // When saving as a draft, explicitly tell the backend to maintain the current status.
            // If it's a new order, it will be a draft (10).
            payload.orderStatusCode = currentOrderData ? currentOrderData.orderStatusCode : 10; 
        }
        // When submitting (isDraft = false), payload.orderStatusCode remains null,
        // allowing the backend to determine the next status.

        let url;
        let method;

        if (isEditMode) {
            if (isFormReadOnlyForStatus30) {
                // Special partial update for read-only state
                url = `/api/v1/orders/dispatch/${currentOrderId}/partial-update`;
                method = 'PUT';

                // --- NEW LOGIC for partial-update ---
                // Add existing IDs to the payload for partial update
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    const mainItemBarcode = pane.dataset.mainBarcode;
                    const payloadItem = payload.items.find(p => p.productBarcode === mainItemBarcode);
                    if (payloadItem) {
                        payloadItem.orderItemId = pane.dataset.mainOrderItemId || null;

                        // Add group IDs
                        pane.querySelectorAll('.sub-items-table tbody tr').forEach(row => {
                            const groupBarcode = row.dataset.barcode;
                            const groupType = row.dataset.itemType; // 'GIFT' or 'ADDON'

                            if (payloadItem.itemGroups && row.dataset.groupId) {
                                const payloadGroup = payloadItem.itemGroups.find(g => g.productBarcode === groupBarcode);
                                if(payloadGroup) {
                                     payloadGroup.orderItemGroupId = row.dataset.groupId;
                                }
                            }
                        });
                    }
                });
                 // --- END OF NEW LOGIC for partial-update ---

            } else {
                // Standard update for editable draft
                url = `/api/v1/orders/dispatch/${currentOrderId}`;
                method = 'PUT';
            }
        } else {
            // Creating a new order
            url = '/api/v1/orders/dispatch';
            method = 'POST';
        }

        // The draft flag is only relevant for determining success message and redirect,
        // the backend status is handled within the payload or backend logic.

        try {
            const response = await fetchAuthenticated(url, {
                method: method,
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errData = await response.json();
                const friendlyMessage = parseErrorMessage(errData.message || '未知錯誤');
                throw new Error(`存檔失敗: ${friendlyMessage}`);
            }

            const result = await response.json();

            if (isDraft) {
                showToast('訂單已儲存', 'success');
                setTimeout(() => {
                    if (!isEditMode && result.data && result.data.orderId) {
                        window.location.href = `dispatch_product_order_form.html?orderId=${result.data.orderId}`;
                    } else {
                        location.reload();
                    }
                }, 1000); // 1-second delay
            } else {
                showToast('訂單已成功送出', 'success');
                setTimeout(() => {
                    window.location.href = `order_detail.html?orderId=${result.data.orderId}`;
                }, 1000); // 1-second delay
            }

        } catch (error) {
            console.error('存檔草稿失敗:', error);
            const friendlyMessage = parseErrorMessage(error.message);
            showToast(`存檔草稿失敗: ${friendlyMessage}`, 'danger');
        }
    }

    async function handleFinalCheckout() {
        if (!validateAllItemStock()) { 
            showToast('所有商品的「出貨倉庫」都必須選擇，才能結帳送出。', 'warning');
            return;
        }

        if(window.confirm('您確定要結帳送出嗎？此操作將會預扣庫存。')) {
            try {
                const response = await fetchAuthenticated(`/api/v1/orders/${currentOrderId}/dispatch-checkout`, {
                    method: 'POST'
                });
                if (!response.ok) {
                    const errData = await response.json();
                    const friendlyMessage = parseErrorMessage(errData.message || '結帳送出失敗');
                    throw new Error(friendlyMessage);
                }
                const result = await response.json();
                showToast('訂單已成功結帳送出', 'success');
                setTimeout(() => {
                    window.location.href = `order_detail.html?orderId=${result.data.orderId}`;
                }, 1000);
            } catch (error) {
                console.error('結帳送出失敗:', error);
                const friendlyMessage = parseErrorMessage(error.message);
                showToast(`結帳送出失敗: ${friendlyMessage}`, 'danger');
            }
        }
    }

    /**
     * Main initialization function, orchestrates the form setup.
     */
    async function initDispatchOrderForm() {
        // Attach listeners first to ensure they're ready.
        attachEventListeners();

        // Initialize address component early so it's always available.
        installationAddressComponent = new AddressComponent('installation-address-component-placeholder');
        document.getElementById('installation-address-component-placeholder').addEventListener('change', () => {
            const addressData = installationAddressComponent.getData();
            const fullAddressInput = document.getElementById('installationAddress');
            if(fullAddressInput) {
                fullAddressInput.value = addressData.fullAddress;
            }
        });

        const urlParams = new URLSearchParams(window.location.search);
        currentOrderId = urlParams.get('orderId');
        isEditMode = !!currentOrderId;

        await populateCompanyDivision();
        // Ensure stores are loaded before attempting to populate the form in edit mode.
        await loadStores(); 
        await Promise.all([
            loadDistributors(),
            loadSalespersons(), // Initial load with no selection
            loadTechnicians()   // Load technicians
        ]);

        handleInvoiceTypeChange();

        const copyFromId = urlParams.get('copyFromId');
        if (copyFromId) {
            isEditMode = false; // We are in creation mode, but copying data
            currentOrderId = null; // Ensure we don't accidentally update the old order
            formTitle.textContent = '新增派工商品訂單 (複製)';
            document.getElementById('page-main-title').textContent = '新增派工商品訂單 (複製)';
            await loadOrderForEditing(copyFromId, true); // Pass a flag to indicate it's a copy
        } else if (isEditMode) {
            formTitle.textContent = '修改派工商品訂單';
            // Add a badge for the title
            const titleElement = document.getElementById('page-main-title');
            if (titleElement) {
                titleElement.innerHTML = `修改派工商品訂單 <span id="currentStatusBadge" class="badge rounded-pill bg-secondary fs-6 ms-2" style="display: none;"></span>`;
            }
            await loadOrderForEditing(currentOrderId);
        } else {
            // --- 新增模式邏輯 ---
            const today = new Date().toISOString().split('T')[0];
            if (orderDateInput) orderDateInput.value = today;
            if (invoiceDateInput) invoiceDateInput.value = today;

            addMainProductTab(null); 

            if (storeSelect) {
                storeSelect.dispatchEvent(new Event('change'));
            }

            // Show only Save and Submit for a new order
            const saveDraftBtn = document.getElementById('save-draft-btn');
            const submitOrderBtn = document.getElementById('submit-order-btn');
            if (saveDraftBtn) saveDraftBtn.style.display = 'inline-block';
            if (submitOrderBtn) submitOrderBtn.style.display = 'inline-block';
        }

        // 初始化頁面上已有的靜態 Tooltip
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

        // Finally, update all calculations and button states, and disable button initially
        updateTotalAmountsUI();
        const submitOrderBtn = document.getElementById('submit-order-btn');
        if(submitOrderBtn) {
            submitOrderBtn.disabled = true;
        }
    }

    /**
     * Fetches and populates the form when in edit mode or copying.
     */
    async function loadOrderForEditing(orderId, isCopy = false) {
        try {
            const response = await fetchAuthenticated(`/api/v1/orders/dispatch/${orderId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API did not return order data.");
            populateForm(apiResponse.data, isCopy);
        } catch (error) {
            console.error('Failed to load order for editing/copying:', error);
            showToast('載入訂單資料失敗', 'danger');
        }
    }

    /**
     * Populates the form with data from a fetched order.
     * @param {object} order - The order data DTO.
     * @param {boolean} isCopy - Flag to indicate if we are copying, which affects some fields.
     */
    async function populateForm(order, isCopy = false) {
        if (!order) return;

        currentOrderData = order; // Store fetched data globally

        const isStatus30 = order.orderStatusCode === 30 && !isCopy;
        isFormReadOnlyForStatus30 = isStatus30; // Set the global flag

        // --- Button State Management based on initial status ---
        const saveDraftBtn = document.getElementById('save-draft-btn');
        const submitOrderBtn = document.getElementById('submit-order-btn');
        const checkoutBtn = document.getElementById('checkout-btn');
        const allButtons = [saveDraftBtn, submitOrderBtn, checkoutBtn, hqReturnBtn, hqRejectBtn, hqApproveBtn, resubmitForHqReviewBtn, copyOrderBtn, createDispatchRepairBtn, deleteBtn];
        allButtons.forEach(btn => btn && (btn.style.display = 'none'));

        setFormReadOnly(false); // Default to editable

        if ((order.orderStatusCode > 30 || order.orderStatusCode < 0) && !isCopy) {
            setFormReadOnly(true);
        } else if (isStatus30) { 
            if(saveDraftBtn) {
                saveDraftBtn.textContent = '儲存變更';
                saveDraftBtn.style.display = 'inline-block'; 
            }
            if(submitOrderBtn) submitOrderBtn.style.display = 'none';
            if(checkoutBtn) checkoutBtn.style.display = 'none';
            setFormReadOnly(true); // Keep form mostly read-only
        } else if (order.orderStatusCode === 40 && !isCopy) { // ** NEW LOGIC FOR STATUS 40 **
            setFormReadOnly(true);
            if (hqReturnBtn) hqReturnBtn.style.display = 'inline-block';
            if (hqRejectBtn) hqRejectBtn.style.display = 'inline-block';
            if (hqApproveBtn) hqApproveBtn.style.display = 'inline-block';
        } else if (order.orderStatusCode === 43 && !isCopy) { // ** NEW LOGIC FOR STATUS 43 **
            setFormReadOnly(false); // Form is editable
            if (resubmitForHqReviewBtn) resubmitForHqReviewBtn.style.display = 'inline-block';
            // Also show save draft button
            if (saveDraftBtn) saveDraftBtn.style.display = 'inline-block';
        } else if (order.orderStatusCode === 46 && !isCopy) { // ** NEW LOGIC FOR STATUS 46 **
            setFormReadOnly(true);
            if (copyOrderBtn) copyOrderBtn.style.display = 'inline-block';
        } else if (order.orderStatusCode === 49 && !isCopy) { // ** NEW LOGIC FOR STATUS 49 **
            setFormReadOnly(true);
            if (createDispatchRepairBtn) createDispatchRepairBtn.style.display = 'inline-block';
        } else { // Draft or Copy status
            setFormReadOnly(false);
            if (saveDraftBtn) saveDraftBtn.style.display = 'inline-block';
            if (submitOrderBtn) {
                submitOrderBtn.textContent = '送出';
                submitOrderBtn.classList.replace('btn-success', 'btn-primary');
                submitOrderBtn.style.display = 'inline-block';
            }
            // ** ONLY show delete button if it's an existing draft, not a new order **
            if (isEditMode && deleteBtn) deleteBtn.style.display = 'inline-block';
            if (checkoutBtn) checkoutBtn.style.display = 'none';
        }

        // --- Populate Order Info & Status ---
        // Ensure dropdowns are populated before setting their values
        if (companyDivisionSelect) {
            // This is usually populated synchronously, but we ensure the value is set correctly.
            companyDivisionSelect.value = order.companyDivisionCode;
        }
        if (storeSelect) {
            // The value is set here. We must ensure loadStores() has completed before this.
            storeSelect.value = order.storeId;
        }

        if(distributorSelect) distributorSelect.value = order.distributorId;

        // Populate Order Number in the header
        const orderNumberDisplay = document.getElementById('orderNumberDisplay');
        if (orderNumberDisplay) {
            orderNumberDisplay.textContent = `#${order.orderNumber || ''}`;
        }

        // CRITICAL FIX: Load warehouses synchronously based on the store from the order data
        // This MUST complete before we try to populate any rows that need the warehouse list.
        await updateAvailableWarehouses(order.storeId);

        // Now that warehouses are loaded, we can proceed to populate other dropdowns and the form
        await loadSalespersons(order.storeId, order.distributorId);
        if(salespersonSelect) salespersonSelect.value = order.userAccountId; // Corrected to use userAccountId for salesperson

        if(orderDateInput) orderDateInput.value = order.orderDate ? order.orderDate.substring(0, 10) : '';

        // After populating store/distributor, load relevant promotions and set the value
        await loadPromotions(order.storeId, order.distributorId);
        if(promotionSelect && order.promotionId) {
            promotionSelect.value = order.promotionId;
        }

        const statusBadge = document.getElementById('currentStatusBadge');
        if (statusBadge) {
            if (isCopy) {
                statusBadge.textContent = '草稿 (複製)';
                statusBadge.className = 'badge rounded-pill bg-info fs-6 ms-2';
            } else {
            statusBadge.textContent = order.orderStatusDescription || '草稿';
                statusBadge.className = 'badge rounded-pill bg-secondary fs-6 ms-2';
            }
            statusBadge.style.display = 'inline-block';
        }

        // --- Populate Customer Info ---
        populateCustomerInfo({
            customerId: order.memberId,
            customerName: order.customerName,
            phoneNumber: order.customerPhone
        });

        // --- Populate Dispatch Info ---
        document.getElementById('contactName').value = order.contactName || '';
        document.getElementById('contactPhone').value = order.contactPhone || '';
        document.getElementById('installationDate').value = order.installationDate ? order.installationDate.substring(0, 10) : '';
        document.getElementById('installationTimeSlot').value = order.installationTimeSlot || '';
        document.getElementById('technicianId').value = order.technicianId || '';
        document.getElementById('expectedCompletionDate').value = order.expectedCompletionDate ? order.expectedCompletionDate.substring(0, 10) : '';
        document.getElementById('dispatchNotes').value = order.dispatchNotes || '';

        // --- CORRECTED LOGIC for Populating Items with Nested Dispatch Groups ---
        const tabsContainer = document.getElementById('dispatch-group-tabs');
        const contentContainer = document.getElementById('dispatch-group-tab-content');

        if (!tabsContainer || !contentContainer) {
            console.error("Tab containers not found!");
            return;
        }

        tabsContainer.innerHTML = '';
        contentContainer.innerHTML = '';
        tabCounter = 0; // Reset tab counter

        if (order.items && order.items.length > 0) {
            order.items.forEach(mainItem => {
                if (mainItem.itemTypeCode === 0) { // Process only main products to create tabs
                    const mainProductForTab = {
                        orderItemId: mainItem.orderItemId, // Pass the ID
                        productBarcode: mainItem.productBarcode,
                        productName: mainItem.productName,
                        listPrice: mainItem.listPrice, // Use listPrice for original price
                        salePrice: mainItem.finalPricePerItem // Use finalPricePerItem for sale price
                    };

                    addMainProductTab(mainProductForTab);

                    const newPane = contentContainer.querySelector('.tab-pane:last-child');
                    if (newPane) {
                        // FIX for Bug 2: Store the original gift total from the DTO into the pane's dataset
                        newPane.dataset.originalGiftTotal = mainItem.originalGiftTotal || 0;

                        const snInput = newPane.querySelector('.serial-number');
                        const whSelect = newPane.querySelector('.warehouse-select');

                        if(snInput) snInput.value = mainItem.mahjongTableSerialNumber || '';
                        if(whSelect) {
                            // If warehouseName is provided, create a pre-selected option
                            if (mainItem.warehouseCode && mainItem.warehouseName) {
                                whSelect.innerHTML = `<option value="${mainItem.warehouseCode}" selected>${mainItem.warehouseName}</option>`;
                            }
                            // The full list of warehouses will be populated later or if needed
                            populateWarehouseDropdown(whSelect, mainItem.warehouseCode, mainItem.productBarcode);
                        }

                        // Populate sub-items (gifts and addons) which are now itemGroups
                        const giftsTableBody = newPane.querySelector('.gifts-table tbody');
                        const addonsTableBody = newPane.querySelector('.addons-table tbody');

                        if (mainItem.itemGroups && mainItem.itemGroups.length > 0) {
                            mainItem.itemGroups.forEach(group => {
                                const subItemProduct = {
                                    orderItemGroupId: group.orderItemGroupId, // Pass the ID
                                    productBarcode: group.productBarcode,
                                    productName: group.productName,
                                    isAwait: group.isAwait, // Pass the status
                                    warehouseCode: group.warehouseId,
                                    warehouseName: group.warehouseName,
                                    quantity: group.quantity,
                                    salePrice: group.unitPrice,
                                    listPrice: group.listPrice,
                                    itemTypeCode: group.itemTypeCode
                                };
                                const itemTypeStr = group.itemTypeCode === 1 ? 'GIFT' : 'ADDON';
                                const targetTable = newPane.querySelector(itemTypeStr === 'GIFT' ? '.gifts-table tbody' : '.addons-table tbody');
                                addProductItemRow(targetTable, subItemProduct, itemTypeStr);
                            });
                        }
                    }
                }
            });
        }
        // --- END OF CORRECTED LOGIC ---

        // --- Populate Invoice Info ---
        const invoiceTypeRadio = document.querySelector(`input[name="invoiceTypeBtn"][value="${order.invoiceTypeCode}"]`);
        if (invoiceTypeRadio) invoiceTypeRadio.checked = true;
        handleInvoiceTypeChange(); // This will show/hide relevant fields
        document.getElementById('taxIdNumber').value = order.taxIdNumber || '';
        document.getElementById('invoiceCompanyTitle').value = order.invoiceCompanyTitle || '';
        if(invoiceDateInput) invoiceDateInput.value = order.invoiceDate ? order.invoiceDate.substring(0, 10) : '';
        document.getElementById('invoiceNumber').value = order.invoiceNumber || '';

        // Populate new store invoice fields
        document.getElementById('storeInvoiceAmount').value = order.storeInvoiceAmount || '';
        document.getElementById('storeInvoiceCompanyTitle').value = order.storeInvoiceCompanyTitle || '';
        document.getElementById('storeTaxIdNumber').value = order.storeTaxIdNumber || '';

        // Check if the store invoice amount was manually set
        if (order.storeInvoiceAmount && order.storeInvoiceAmount !== order.productsTotalAmount) {
            storeInvoiceAmountManuallyChanged = true;
        }

        // If copying, clear fields that should be new
        if (isCopy) {
            const today = new Date().toISOString().split('T')[0];
            if (orderDateInput) orderDateInput.value = today;
            if (invoiceDateInput) invoiceDateInput.value = today;
            document.getElementById('invoiceNumber').value = '';
            // Reset order number display if any
            const orderNumberDisplay = document.getElementById('orderNumberDisplay');
            if (orderNumberDisplay) orderNumberDisplay.textContent = '(新訂單)';
        }

        // --- Populate Payment Info ---
        paymentContainer.innerHTML = '';
        if (order.paymentDetails && order.paymentDetails.length > 0) {
            order.paymentDetails.forEach(p => addPaymentMethodRowAndPopulate(p));
        }

        // --- Populate Remarks ---
        document.getElementById('orderRemarks').value = order.remarks || '';

        // Finally, update all calculations and button states
        updateTotalAmountsUI();
        validateAllItemStock();

        if (installationAddressComponent) {
            // 將後端回傳的結構化地址欄位對應到元件的 setData 方法
            const addressData = {
                addressCityName: order.installationAddressCity,
                addressDistrictName: order.installationAddressDistrict,
                addressStreetName: order.installationAddressStreet,
                addressLane: order.installationAddressLane,
                addressAlley: order.installationAddressAlley,
                addressNumber: order.installationAddressNumber,
                addressFloor: order.installationAddressFloor,
                addressUnit: order.installationAddressUnit,
            };
            installationAddressComponent.setData(addressData);
            setTimeout(() => {
                document.getElementById('installation-address-component-placeholder').dispatchEvent(new Event('change'));
            }, 500);
        }
    }

    // New helper function to add and populate a payment row
    function addPaymentMethodRowAndPopulate(paymentDetail) {
        const type = paymentDetail.paymentMethodCode.toLowerCase();
        const paymentId = `payment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; // More unique ID
        let contentHtml = '';
        const removeButtonHtml = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn"><i class="bi bi-trash"></i></button></div>`;

        // 1. Create the container first and add it to the DOM
        const paymentRowContainer = document.createElement('div');
        paymentRowContainer.className = 'payment-item row g-3 mb-2 align-items-center';
        paymentRowContainer.id = paymentId;
        paymentRowContainer.dataset.type = type;
        if(paymentContainer) paymentContainer.appendChild(paymentRowContainer);

        // 2. Define the inner HTML based on type
        switch (type) {
            case 'cash':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div><div class="col-md-4"></div>`;
                break;
            case 'credit_card':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                    <div class="col-md-3 position-relative">
                        <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                        <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'bank_transfer':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'technician_collection':
                const technicianSelect = document.getElementById('technicianId');
                const selectedTechnicianName = technicianSelect.options[technicianSelect.selectedIndex]?.text || '請先指派技師';
                contentHtml = `
                    <div class="col-md-3"><input type="text" class="form-control" value="技師代收款" readonly></div>
                    <div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div>
                    <div class="col-md-4"><input type="text" class="form-control technician-name-display" value="${selectedTechnicianName}" readonly></div>`;
                break;
            case 'staff_collection':
                contentHtml = `
                    <div class="col-md-3"><input type="text" class="form-control" value="人員代收款" readonly></div>
                    <div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div>
                    <div class="col-md-4"><input type="text" class="form-control" placeholder="輸入姓名或員編"></div>`;
                break;
        }

        // 3. Set the inner HTML of the container we just added
        paymentRowContainer.innerHTML = contentHtml + removeButtonHtml;

        // 4. Now, safely query within the specific container and populate data
        const newRow = document.getElementById(paymentId);
        if (!newRow) return;

        newRow.querySelector('.payment-amount').value = paymentDetail.amount;

        if (type === 'credit_card') {
            const cardNumberInput = newRow.querySelector('.card-number');
            if(cardNumberInput) cardNumberInput.value = paymentDetail.cardNumber || '';

            const paymentTypeSelect = newRow.querySelector('.card-payment-type');
            const installmentsInput = newRow.querySelector('.card-installments');
            if (paymentTypeSelect && installmentsInput) {
                if (paymentDetail.cardInstallments > 1) {
                    paymentTypeSelect.value = '2';
                    installmentsInput.style.display = 'block';
                    installmentsInput.value = paymentDetail.cardInstallments;
            } else {
                    paymentTypeSelect.value = '0';
                    installmentsInput.style.display = 'none';
                    installmentsInput.value = '1';
                }
            }
        } else if (type === 'bank_transfer') {
            const bankNameInput = newRow.querySelector('.bank-name');
            const remitterInput = newRow.querySelector('.remitter-account');
            const bankAccountInput = newRow.querySelector('.bank-account');
            if(bankNameInput) bankNameInput.value = paymentDetail.bankName || '';
            if(remitterInput) remitterInput.value = paymentDetail.remitterAccountLastFive || '';
            if(bankAccountInput) bankAccountInput.value = paymentDetail.bankAccountLastFive || '';
        }
    }

    /**
     * Handles the product search button click for dispatch orders.
     */
    async function handleProductSearch(isModalSearch = false) {
        const storeId = storeSelect.value;
        const distributorId = document.getElementById('distributorSelect').value;

        // --- NEW VALIDATION: Store or Distributor must be selected before search ---
        if (!storeId && !distributorId) {
            showToast('請先選擇「開單門市」或「銷售據點」才能搜尋商品。', 'warning');
            return;
        }

        let keyword = '';
        // 只有在搜尋主商品時，才從主輸入框獲取關鍵字
        if (isSearchingForMainProduct) {
            const keywordInput = isModalSearch ? document.getElementById('productSearchKeywordModal') : document.getElementById('productKeyword');
            keyword = keywordInput.value.trim();
            if (!keyword) {
                showToast('請輸入商品關鍵字', 'warning');
                return;
            }
        } else {
            // 對於贈品/加購品，如果 modal 內的搜尋框有值，則使用它，否則關鍵字為空
            const keywordInputModal = document.getElementById('productSearchKeywordModal');
            if (isModalSearch) {
                 keyword = keywordInputModal.value.trim();
            }
        }

        const searchResultsContainer = document.getElementById('productSearchResultsModal');
        searchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';

        if (!isModalSearch) {
            const productSearchModal = new bootstrap.Modal(document.getElementById('productSearchModal'));
            productSearchModal.show();
        }

        try {
            let url = '';
            if (isSearchingForMainProduct) {
                url = `/api/v1/product-settings/search-dispatch-products?keyword=${encodeURIComponent(keyword)}`;
            } else {
                url = `/api/v1/product-settings/search-accessories?keyword=${encodeURIComponent(keyword)}`;
            }

            if (storeId) url += `&storeId=${storeId}`;
            if (distributorId) url += `&distributorId=${distributorId}`; // 注意：配件搜尋可能不需要此參數

            const response = await fetchAuthenticated(url);
            if(!response.ok) throw new Error('搜尋商品失敗');

            const apiResponse = await response.json();
            const products = apiResponse.data;

            searchResultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const salePrice = product.salePrice !== null ? product.salePrice : 0;
                    const listPrice = product.listPrice !== null ? product.listPrice : 0;
                    const productHtml = `
                        <a href="#" class="list-group-item list-group-item-action select-product-btn" 
                           data-product-barcode="${product.productBarcode}" 
                           data-product-name="${product.productName}" 
                           data-sale-price="${salePrice}"
                           data-list-price="${listPrice}"
                           data-is-dispatch="${product.isDispatchProduct}"
                           data-is-main="${product.isMain}"
                           data-gift-bundle='${JSON.stringify(product.giftBundleItems || [])}'>
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${product.productName}</h6>
                            </div>
                            <p class="mb-1">${product.productBarcode}</p>
                            <small class="text-muted">原價: NT$ ${listPrice.toLocaleString()}</small>
                        </a>
                    `;
                    searchResultsContainer.insertAdjacentHTML('beforeend', productHtml);
                });
            } else {
                searchResultsContainer.innerHTML = '<div class="list-group-item">找不到符合條件的商品。</div>';
            }
        } catch (error) {
            console.error('Product search error:', error);
            searchResultsContainer.innerHTML = '<div class="list-group-item text-danger">搜尋時發生錯誤。</div>';
        }
    }

    function addProductItemRow(targetTableBody, product, itemType) {
        if (!targetTableBody) return;
        const rowId = `item-${product.productBarcode}-${Date.now()}`;
        const newRow = document.createElement('tr');
        newRow.id = rowId;
        newRow.dataset.barcode = product.productBarcode;
        newRow.dataset.itemType = itemType;
        newRow.dataset.groupId = product.orderItemGroupId || ''; // Store the group ID

        const salePrice = product.salePrice || 0;
        const listPrice = product.listPrice || 0;
        const quantity = product.quantity || 1;
        const subtotal = salePrice * quantity;
        const disabledAttr = isFormReadOnlyForStatus30 ? 'disabled' : '';
        const isAwaiting = product.isAwait === 1;

        newRow.innerHTML = `
            <td>${product.productName}</td>
            <td><select class="form-select form-select-sm dispatch-type-select" ${disabledAttr}></select></td>
            <td><span class="price-span list-price">${listPrice.toLocaleString()}</span></td>
            <td><span class="price-span sale-price">${salePrice.toLocaleString()}</span></td>
            <td><input type="number" class="form-control form-control-sm quantity-input" value="${quantity}" min="1" ${disabledAttr}></td>
            <td class="item-subtotal">${subtotal.toLocaleString()}</td>
            <td><select class="form-select form-select-sm warehouse-select" required><option value="">請選擇</option></select></td>
            <td colspan="2">
                <div class="d-flex justify-content-center gap-1">
                    <button type="button" class="btn ${isAwaiting ? 'btn-dark' : 'btn-outline-secondary'} btn-sm awaiting-material-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="設為待料" ${disabledAttr}>
                        <i class="bi bi-hourglass-split"></i>
                    </button>
                    <button type="button" class="btn btn-danger btn-sm remove-item-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="刪除" ${disabledAttr}>
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;

        targetTableBody.appendChild(newRow);

        // 為附屬品的倉庫下拉選單填充選項
        const warehouseSelect = newRow.querySelector('.warehouse-select');
        if (warehouseSelect) {
            if (product.warehouseCode && product.warehouseName) {
                warehouseSelect.innerHTML = `<option value="${product.warehouseCode}" selected>${product.warehouseName}</option>`;
            }
            populateWarehouseDropdown(warehouseSelect, product.warehouseCode, product.productBarcode);
        }

        // 為新的派工類型下拉選單填充選項 (排除 "自載展示機")
        const dispatchTypeSelectForSubItem = newRow.querySelector('.dispatch-type-select');
        if (dispatchTypeSelectForSubItem) {
            // 檢查主商品的出貨方式
            const currentPane = targetTableBody.closest('.tab-pane');
            const mainProductDispatchSelect = currentPane ? currentPane.querySelector('.card-body .dispatch-type-select') : null;
            const mainProductShipmentMethod = mainProductDispatchSelect ? mainProductDispatchSelect.value : null;
            
            // 如果主商品選擇了「店取」或「自載展示機」，直接限制為店取
            if (mainProductShipmentMethod === '0' || mainProductShipmentMethod === '2') {
                restrictDispatchTypeToStorePickup(dispatchTypeSelectForSubItem);
            } else {
                // 否則填充完整選項（排除自載展示機）
                populateRequireDispatchTypes(dispatchTypeSelectForSubItem, true).then(() => {
                    // 如果是編輯模式且已有值，則設定為該值，否則預設為 '派工'
                    if (product.requiresDispatch !== undefined) {
                        dispatchTypeSelectForSubItem.value = product.requiresDispatch;
                    } else {
                        dispatchTypeSelectForSubItem.value = '1';
                    }
                });
            }
        }

        // 初始化新按鈕的 Tooltip
        const newTooltips = newRow.querySelectorAll('[data-bs-toggle="tooltip"]');
        newTooltips.forEach(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

        // 為新按鈕綁定事件
        newRow.querySelector('.remove-item-btn').addEventListener('click', function() {
            newRow.remove();
            updateTotalAmountsUI();
        });
        // ... 其他事件綁定

        // After appending, find the select and set its value
        const dispatchTypeSelectInRow = newRow.querySelector('.dispatch-type-select');
        if (dispatchTypeSelectInRow && product.requiresDispatch !== undefined) {
            dispatchTypeSelectInRow.value = product.requiresDispatch;
        }

        applyPromotionsToItems();
    }

    function toggleNoItemsMessage() {
        const noItemsMsg = document.getElementById('no-items-message');
        if (noItemsMsg) {
            noItemsMsg.style.display = tableBody.rows.length > 0 ? 'none' : 'block';
        }
    }

    /**
     * Updates the state of the gift section (button and totals) based on whether a main product is selected.
     * @param {HTMLElement} tabPane - The specific tab pane element to update.
     */
    function updateGiftSectionState(tabPane) {
        if (!tabPane) return;

        const addGiftBtn = tabPane.querySelector('.add-sub-item-btn[data-item-type="GIFT"]');
        const originalGiftTotalEl = tabPane.querySelector('.original-gift-total');
        const isPlaceholder = tabPane.dataset.isPlaceholder === 'true';

        if (addGiftBtn) {
            addGiftBtn.disabled = isPlaceholder;
        }

        if (isPlaceholder && originalGiftTotalEl) {
            // If it's a placeholder (no main product), reset gift totals.
            originalGiftTotalEl.textContent = '0';
            // Also reset the exchanged gift total
            const exchangedGiftTotalEl = tabPane.querySelector('.exchanged-gift-total');
            if (exchangedGiftTotalEl) {
                exchangedGiftTotalEl.textContent = '0';
            }
        }
    }

    /**
     * 新增一個主商品 TAB 頁籤和對應的內容面板
     */
    function addMainProductTab(mainProduct) {
        tabCounter++;
        const tabId = `group-tab-${tabCounter}`;
        const paneId = `group-pane-${tabCounter}`;
        const isPlaceholder = mainProduct === null;

        const tabTitle = isPlaceholder ? `主商品 ${tabCounter}` : mainProduct.productName;
        const mainProductBarcode = isPlaceholder ? '' : mainProduct.productBarcode;
        const mainProductName = isPlaceholder ? '待選擇...' : mainProduct.productName;
        const listPrice = isPlaceholder ? 0 : (mainProduct.listPrice || 0);
        const salePrice = isPlaceholder ? 0 : (mainProduct.salePrice || 0);

        // 1. 建立新的 TAB 頁籤按鈕
        const newTabHtml = `
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="${tabId}" data-bs-toggle="tab" data-bs-target="#${paneId}" href="#" role="tab" aria-controls="${paneId}" aria-selected="false">
                    <span>${tabTitle}</span>
                    <button type="button" class="btn-close btn-sm ms-2" aria-label="Close"></button>
                </a>
            </li>`;
        document.getElementById('dispatch-group-tabs').insertAdjacentHTML('beforeend', newTabHtml);

        // 2. 建立新的 TAB 內容面板
        const newPaneHtml = `
            <div class="tab-pane fade" id="${paneId}" role="tabpanel" aria-labelledby="${tabId}" 
                 data-main-barcode="${mainProductBarcode}" 
                 data-main-product-name="${mainProductName}" 
                 data-is-placeholder="${isPlaceholder}"
                 data-main-order-item-id="${isPlaceholder ? '' : (mainProduct.orderItemId || '')}">
                <div class="card mb-3 border-top-0">
                    <div class="card-body">
                        <h5>主商品資訊</h5>
                        <div class="row g-3">
                            <div class="col-md-3"><label class="form-label">商品名稱</label><input type="text" class="form-control main-product-name-display" value="${mainProductName}" readonly></div>
                            <div class="col-md-3"><label class="form-label">出貨方式</label><select class="form-select dispatch-type-select"></select></div>
                            <div class="col-md-3"><label class="form-label">原價</label><input type="text" class="form-control list-price-display" value="${listPrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">活動價</label><input type="text" class="form-control sale-price-display" value="${salePrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">數量</label><input type="text" class="form-control" value="1" readonly></div>
                            <div class="col-md-3"><label class="form-label">金額</label><input type="text" class="form-control amount-display" value="${salePrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">電動桌機身號碼</label><input type="text" class="form-control serial-number" placeholder="機身號碼"></div>
                            <div class="col-md-3"><label class="form-label">出貨倉庫</label><select class="form-select warehouse-select"></select></div>
                        </div>
                        <hr>
                        <h5>贈品</h5>
                        <button type="button" class="btn btn-sm btn-outline-info add-sub-item-btn mb-2" data-item-type="GIFT">新增贈品</button>
                        <div class="table-responsive">
                            <table class="table table-sm sub-items-table gifts-table">
                                <thead>
                                    <tr>
                                        <th>商品名稱</th>
                                        <th>出貨方式</th>
                                        <th>原價</th>
                                        <th>活動價</th>
                                        <th>數量</th>
                                        <th>小計</th>
                                        <th>出貨倉庫</th>
                                        <th></th> <!-- 代料欄位留白 -->
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <div class="text-end mt-2 gift-summary">
                            <span>原贈品金額總計：<span class="original-gift-total fw-bold">0</span>；</span>
                            <span>換贈品金額總計：<span class="exchanged-gift-total fw-bold">0</span></span>
                        </div>

                        <!-- 加購品區塊 -->
                        <hr>
                        <h5>加購品</h5>
                        <button type="button" class="btn btn-sm btn-outline-primary add-sub-item-btn mb-2" data-item-type="ADDON">新增加購品</button>
                        <div class="table-responsive">
                            <table class="table table-sm sub-items-table addons-table">
                                <thead>
                                    <tr>
                                        <th>商品名稱</th>
                                        <th>出貨方式</th>
                                        <th>原價</th>
                                        <th>活動價</th>
                                        <th>數量</th>
                                        <th>小計</th>
                                        <th>出貨倉庫</th>
                                        <th></th> <!-- 代料欄位留白 -->
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>`;
        document.getElementById('dispatch-group-tab-content').insertAdjacentHTML('beforeend', newPaneHtml);

        // 為新面板中的倉庫下拉選單填充選項
        const newWarehouseSelect = document.querySelector(`#${paneId} .warehouse-select`);
        if (newWarehouseSelect) {
            populateWarehouseDropdown(newWarehouseSelect, null, mainProductBarcode);
        } else {
            console.error(`Could not find warehouse select element in new pane with ID: ${paneId}`);
        }

        // 為新的派工類型下拉選單填充選項
        const newDispatchTypeSelect = document.querySelector(`#${paneId} .dispatch-type-select`);
        if (newDispatchTypeSelect) {
            populateRequireDispatchTypes(newDispatchTypeSelect).then(() => {
                newDispatchTypeSelect.value = '1'; // 預設為派工
            });
        }

        // 3. 啟用並顯示新的 TAB
        const newTabTrigger = document.getElementById(tabId);
        if (newTabTrigger) {
            const tab = new bootstrap.Tab(newTabTrigger);
            tab.show();
        }

        // 4. Update the state of the gift section for the new tab
        const newPaneElement = document.getElementById(paneId);
        updateGiftSectionState(newPaneElement);
    }

    /**
     * 檢查所有主商品的出貨方式
     * @returns {Array} 返回所有主商品的出貨方式陣列
     */
    function getAllMainProductShipmentMethods() {
        const shipmentMethods = [];
        document.querySelectorAll('.tab-pane').forEach(pane => {
            if (pane.dataset.isPlaceholder === 'true') {
                return; // 跳過佔位符 tab
            }
            // 尋找主商品的出貨方式選單
            const mainProductDispatchSelect = pane.querySelector('.card-body .dispatch-type-select');
            if (mainProductDispatchSelect && mainProductDispatchSelect.value) {
                shipmentMethods.push(mainProductDispatchSelect.value);
            }
        });
        return shipmentMethods;
    }

    /**
     * 檢查是否所有主商品都是店取(0)
     * @returns {boolean} 如果所有主商品都是店取則返回 true
     */
    function areAllMainProductsStorePickup() {
        const shipmentMethods = getAllMainProductShipmentMethods();
        return shipmentMethods.length > 0 && shipmentMethods.every(method => method === '0');
    }

    /**
     * 檢查主商品是否需要倉庫驗證
     * @param {HTMLElement} pane - tab pane 元素
     * @returns {boolean} 如果需要倉庫驗證則返回 true
     */
    function shouldValidateWarehouseForPane(pane) {
        const mainProductDispatchSelect = pane.querySelector('.card-body .dispatch-type-select');
        const shipmentMethod = mainProductDispatchSelect ? mainProductDispatchSelect.value : null;
        // 店取(0)或自載展示機(2)不需要倉庫驗證
        return shipmentMethod !== '0' && shipmentMethod !== '2';
    }

    /**
     * 處理主商品出貨方式變更，並限制贈品和加購品的出貨方式選項
     * @param {HTMLElement} currentPane - 當前的 tab pane 元素
     * @param {string} mainProductShipmentMethod - 主商品選擇的出貨方式 code
     */
    function handleMainProductShipmentMethodChange(currentPane, mainProductShipmentMethod) {
        if (!currentPane) return;
        
        // 獲取該 tab 下所有贈品和加購品的出貨方式下拉選單
        const giftDispatchSelects = currentPane.querySelectorAll('.gifts-table .dispatch-type-select');
        const addonDispatchSelects = currentPane.querySelectorAll('.addons-table .dispatch-type-select');
        
        // 合併所有子商品的出貨方式選單
        const allSubItemDispatchSelects = [...giftDispatchSelects, ...addonDispatchSelects];
        
        // 如果主商品選擇了「店取」(0) 或「自載展示機」(2)
        if (mainProductShipmentMethod === '0' || mainProductShipmentMethod === '2') {
            // 限制所有贈品和加購品只能選擇「店取」(0)
            allSubItemDispatchSelects.forEach(select => {
                restrictDispatchTypeToStorePickup(select);
            });
        } else {
            // 主商品選擇了其他出貨方式，恢復贈品和加購品的完整選項（排除自載展示機）
            allSubItemDispatchSelects.forEach(select => {
                populateRequireDispatchTypes(select, true); // excludeReturnOption = true
            });
        }
    }
    
    /**
     * 限制出貨方式下拉選單只能選擇「店取」
     * @param {HTMLElement} selectElement - 要限制的下拉選單元素
     */
    function restrictDispatchTypeToStorePickup(selectElement) {
        if (!selectElement) return;
        
        // 保存當前選擇的值
        const currentValue = selectElement.value;
        
        // 清空選單並只添加「店取」選項
        selectElement.innerHTML = '<option value="0">店取</option>';
        
        // 強制設定為店取
        selectElement.value = '0';
        
        // 如果之前不是店取，觸發 change 事件以更新相關邏輯
        if (currentValue !== '0') {
            selectElement.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }

    /**
     * Fetches and populates the 'Require Dispatch' dropdown.
     * @param {HTMLElement} selectElement - The select element to populate.
     * @param {boolean} [excludeReturnOption=false] - Whether to exclude the 'RETURN' option.
     */
    async function populateRequireDispatchTypes(selectElement, excludeReturnOption = false) {
        if (!selectElement) return;
        selectElement.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await fetchAuthenticated('/api/v1/enums/require-dispatch-types');
            if (!response.ok) throw new Error('無法載入派工類型');
            const apiResponse = await response.json();
            let types = apiResponse.data;

            if (excludeReturnOption) {
                types = types.filter(type => type.code != 2); // 排除 code 為 2 的 "自載展示機"
            }

            selectElement.innerHTML = '<option value="">請選擇</option>';
            if (types && types.length > 0) {
                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.code;
                    option.textContent = type.label;
                    selectElement.appendChild(option);
                });
            }
        } catch (error) {
            console.error('無法載入派工類型:', error);
            selectElement.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fills an existing placeholder tab with the selected main product's data.
     * @param {HTMLElement} pane - The placeholder tab pane element.
     * @param {object} product - The product data object from selection.
     */
    function fillPlaceholderTab(pane, product) {
        if (!pane || !product) return;

        const tabId = pane.getAttribute('aria-labelledby');
        const tabLink = document.getElementById(tabId);

        // 1. Update Tab Title
        if (tabLink) {
            tabLink.querySelector('span').textContent = product.productName;
        }

        // 2. Update Pane's dataset
        pane.dataset.isPlaceholder = 'false';
        pane.dataset.mainBarcode = product.productBarcode;
        pane.dataset.mainProductName = product.productName;

        // 3. Populate Pane's input fields
        pane.querySelector('.main-product-name-display').value = product.productName;
        pane.querySelector('.list-price-display').value = (product.listPrice || 0).toLocaleString();
        pane.querySelector('.sale-price-display').value = (product.salePrice || 0).toLocaleString();
        pane.querySelector('.amount-display').value = (product.salePrice || 0).toLocaleString();

        // 4. 更新主商品的倉庫選擇，顯示庫存數量
        const mainWarehouseSelect = pane.querySelector('.warehouse-select');
        if (mainWarehouseSelect) {
            populateWarehouseDropdown(mainWarehouseSelect, null, product.productBarcode);
        }

        // 5. Update gift section state (e.g., enable the "Add Gift" button)
        updateGiftSectionState(pane);

        // 6. Automatically apply promotions if any are selected
        applyPromotionsToItems();

        // 7. Update grand totals
        updateTotalAmountsUI();
    }

    /**
     * Handles the selection of a product from the search modal.
     */
    function handleProductSelection(event) {
        event.preventDefault();
        const target = event.target.closest('.select-product-btn');
        if (!target) return;

        const product = {
            productBarcode: target.dataset.productBarcode,
            productName: target.dataset.productName,
            salePrice: parseFloat(target.dataset.salePrice),
            listPrice: parseFloat(target.dataset.listPrice),
            isDispatchProduct: target.dataset.isDispatch === 'true',
            isMain: target.dataset.isMain === 'true'
        };

        const productSearchModal = bootstrap.Modal.getInstance(document.getElementById('productSearchModal'));
        if(productSearchModal) productSearchModal.hide();

        if (isSearchingForMainProduct) {
            const placeholderPane = document.querySelector('.tab-pane[data-is-placeholder="true"]');

            if (placeholderPane) {
                fillPlaceholderTab(placeholderPane, product);
            } else {
                addMainProductTab(product);
            }

            // Auto-add gift items if they exist
            const giftBundleJson = target.dataset.giftBundle;
            if (giftBundleJson && giftBundleJson.length > 2) { // > 2 to avoid "[]"
                const giftItems = JSON.parse(giftBundleJson);
                setTimeout(() => {
                    const currentPane = document.querySelector('.tab-pane.active');
                    if (currentPane && giftItems.length > 0) {
                        const giftsTableBody = currentPane.querySelector('.gifts-table tbody');
                        if (giftsTableBody) {
                            let originalGiftTotal = 0;
                            giftItems.forEach(gift => {
                                const giftProduct = {
                                    productBarcode: gift.giftProductBarcode,
                                    productName: gift.giftProductName,
                                    quantity: gift.quantity,
                                    salePrice: gift.salePrice || 0,
                                    listPrice: gift.listPrice || 0
                                };
                                addProductItemRow(giftsTableBody, giftProduct, 'GIFT');
                                originalGiftTotal += (gift.salePrice || 0) * (gift.quantity || 1);
                            });
                            currentPane.dataset.originalGiftTotal = originalGiftTotal;
                            updateTotalAmountsUI();
                        }
                    }
                }, 100);
            }

        } else {
            const pane = document.getElementById(currentTargetPaneId);
            if (pane) {
                let targetTable;
                if (currentItemTypeToAdd === 'GIFT') {
                    targetTable = pane.querySelector('.gifts-table tbody');
                } else { // 'ADDON'
                    targetTable = pane.querySelector('.addons-table tbody');
                }
                addProductItemRow(targetTable, product, currentItemTypeToAdd);
            }
        }

        updateTotalAmountsUI();
    }

    // --- Other Helper Functions (Customer Search, UI Updates, etc.) ---

    /**
     * Updates all calculated total amounts in the UI.
     */
    function updateTotalAmountsUI() {
        let totalNetAmount = 0;
        let totalGiftExcess = 0; // Accumulator for excess gift amounts from all tabs

        // Iterate over each tab pane
        document.querySelectorAll('.tab-pane').forEach(pane => {
            // --- Main Product Calculation ---
            const mainProductPrice = parseFloat(pane.querySelector('.sale-price-display')?.value.replace(/,/g, '')) || 0;
            const mainProductQuantity = 1; // Always 1
            const mainProductSubtotal = mainProductPrice * mainProductQuantity;

            // Update main product amount display
            const mainProductAmountDisplay = pane.querySelector('.amount-display');
            if (mainProductAmountDisplay) {
                mainProductAmountDisplay.value = mainProductSubtotal.toLocaleString();
            }
            totalNetAmount += mainProductSubtotal;

            // --- Sub-items Calculation ---
            const originalGiftTotal = parseFloat(pane.dataset.originalGiftTotal) || 0;
            let exchangedGiftTotal = 0;

            // Gifts (calculate for row display AND for total comparison)
            pane.querySelectorAll('.gifts-table tbody tr').forEach(row => {
                const price = parseFloat(row.querySelector('.sale-price')?.textContent.replace(/,/g, '')) || 0;
                const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 0;
                const subtotal = price * quantity;
                const subtotalCell = row.querySelector('.item-subtotal');
                if (subtotalCell) subtotalCell.textContent = subtotal.toLocaleString();
                exchangedGiftTotal += subtotal;
            });

            // Update gift summary UI
            const originalTotalEl = pane.querySelector('.original-gift-total');
            const exchangedTotalEl = pane.querySelector('.exchanged-gift-total');
            if (originalTotalEl) {
                originalTotalEl.textContent = originalGiftTotal.toLocaleString();
            }
            if (exchangedTotalEl) {
                exchangedTotalEl.textContent = exchangedGiftTotal.toLocaleString();
                if (exchangedGiftTotal > originalGiftTotal) {
                    exchangedTotalEl.classList.add('text-danger');
                } else {
                    exchangedTotalEl.classList.remove('text-danger');
                }
            }

            // Calculate this tab's excess gift amount and add to the grand total excess
            const giftExcessAmount = Math.max(0, exchangedGiftTotal - originalGiftTotal);
            totalGiftExcess += giftExcessAmount;

            // Add-ons (calculate for row display AND add to total)
            pane.querySelectorAll('.addons-table tbody tr').forEach(row => {
                const price = parseFloat(row.querySelector('.sale-price')?.textContent.replace(/,/g, '')) || 0;
                const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 0;
                const subtotal = price * quantity;
                const subtotalCell = row.querySelector('.item-subtotal');
                if (subtotalCell) subtotalCell.textContent = subtotal.toLocaleString();
                totalNetAmount += subtotal; // Add addon subtotal to the main total
            });
        });

        // Add the total excess gift amount to the net total
        totalNetAmount += totalGiftExcess;

        // --- New logic for Store Invoice Amount & Tax ---
        let productsTotalAmount = totalNetAmount + totalGiftExcess; // This is the base for store invoice amount

        if (!storeInvoiceAmountManuallyChanged) {
            storeInvoiceAmountInput.value = productsTotalAmount.toFixed(2);
        }

        let storeInvoiceAmount = parseFloat(storeInvoiceAmountInput.value) || 0;

        const invoiceTypeRadio = document.querySelector('input[name="invoiceTypeBtn"]:checked');
        const taxTypeRadio = document.querySelector('input[name="taxTypeBtn"]:checked');
        const invoiceType = invoiceTypeRadio ? invoiceTypeRadio.value : '0';
        const taxType = taxTypeRadio ? taxTypeRadio.value : 'exclusive';

        let finalNetAmount = totalNetAmount;
        let finalTaxAmount = 0;
        let finalGrandTotal = totalNetAmount;

        if (invoiceType === '2' || invoiceType === '3') {
            const taxBaseAmount = storeInvoiceAmount > 0 ? storeInvoiceAmount : productsTotalAmount;
            if (taxType === 'inclusive') {
                // In inclusive tax, the grand total *is* the amount with tax included.
                // The base for calculation should be the user-inputted amount.
                finalGrandTotal = totalNetAmount; // Keep grand total based on items
                finalNetAmount = Math.round(taxBaseAmount / 1.05);
                finalTaxAmount = taxBaseAmount - finalNetAmount;
            } else { // exclusive
                finalTaxAmount = Math.round(taxBaseAmount * 0.05);
                finalGrandTotal = totalNetAmount + finalTaxAmount;
            }
        }

        document.getElementById('productsTotalAmount').textContent = `NT$ ${productsTotalAmount.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        document.getElementById('netAmount').textContent = `NT$ ${finalNetAmount.toLocaleString()}`;
        document.getElementById('taxAmount').textContent = `NT$ ${finalTaxAmount.toLocaleString()}`;
        document.getElementById('grandTotalAmount').textContent = `NT$ ${finalGrandTotal.toLocaleString()}`;

        let totalPaid = 0;
        document.querySelectorAll('.payment-amount').forEach(input => {
            totalPaid += parseFloat(input.value) || 0;
        });

        const amountDue = finalGrandTotal - totalPaid;

        document.getElementById('totalPaidAmountDisplay').textContent = `NT$ ${totalPaid.toLocaleString()}`;
        document.getElementById('amountDueDisplay').textContent = `NT$ ${amountDue.toLocaleString()}`;

        const submitOrderBtn = document.getElementById('submit-order-btn');
        if (submitOrderBtn) {
            const grandTotalIsPositive = finalGrandTotal > 0;
            const isFullyPaid = amountDue === 0;

            // For status 30, the button is always enabled to save warehouse changes.
            if (currentOrderData && currentOrderData.orderStatusCode === 30) {
                submitOrderBtn.disabled = false;
            } else {
                 // In draft or new mode, the button is enabled only if total is positive and fully paid.
                submitOrderBtn.disabled = !(grandTotalIsPositive && isFullyPaid);
            }
        }
    }

    /**
     * Handles the change event for invoice type radio buttons.
     */
    function handleInvoiceTypeChange() {
        const invoiceDetailsContainer = document.getElementById('invoice-details-container');
        const taxIdNumberInput = document.getElementById('taxIdNumber');
        const invoiceCompanyTitleInput = document.getElementById('invoiceCompanyTitle');
        const selectedType = document.querySelector('input[name="invoiceTypeBtn"]:checked')?.value;

        // Logic for store-specific fields
        const storeInvoiceAmountInput = document.getElementById('storeInvoiceAmount');
        const storeTaxIdInput = document.getElementById('storeTaxIdNumber');
        const storeTitleInput = document.getElementById('storeInvoiceCompanyTitle');

        const showStoreFields = selectedType === '2' || selectedType === '3';

        [storeInvoiceAmountInput, storeTaxIdInput, storeTitleInput].forEach(input => {
            if (input) {
                const parentDiv = input.closest('.col-md-4');
                if (parentDiv) parentDiv.style.display = showStoreFields ? '' : 'none';
                input.disabled = !showStoreFields;
            }
        });

        // Logic for original three-part invoice fields
        const showThreePartFields = selectedType === '3';
        if (taxIdNumberInput) taxIdNumberInput.disabled = !showThreePartFields;
        if (invoiceCompanyTitleInput) invoiceCompanyTitleInput.disabled = !showThreePartFields;
        if (!showThreePartFields) {
            if (taxIdNumberInput) taxIdNumberInput.value = '';
            if (invoiceCompanyTitleInput) invoiceCompanyTitleInput.value = '';
        }
    }

    /**
     * Handles the customer search by phone number.
     */
    async function handleCustomerSearch() {
        const phoneInput = document.getElementById('customerPhoneSearch');
        if (!phoneInput) return;
        const phoneNumber = phoneInput.value.trim();
        if (!phoneNumber) {
            showToast('請輸入會員電話號碼', 'warning');
            return;
        }

        try {
            const response = await fetchAuthenticated(`/api/v1/customers/by-phone?phoneNumber=${encodeURIComponent(phoneNumber)}`);
            if (!response.ok) throw new Error('會員查詢失敗');

            const apiResponse = await response.json();
            const customers = apiResponse.data;

            if (!customers || customers.length === 0) {
                showToast('查無此會員資料，請確認電話或快速新增會員。', 'info');
            } else if (customers.length === 1) {
                populateCustomerInfo(customers[0]);
            } else {
                populateCustomerSelectionModal(customers);
                const customerSelectModal = new bootstrap.Modal(document.getElementById('customerSelectModal'));
                customerSelectModal.show();
            }
        } catch (error) {
            console.error('Error searching customer:', error);
            showToast(`查詢會員時發生錯誤: ${error.message}`, 'danger');
        }
    }

    /**
     * Populates the customer selection modal with multiple results.
     */
    function populateCustomerSelectionModal(customers) {
        if (!customerSelectModalBody) return;
        customerSelectModalBody.innerHTML = ''; // Clear previous results
        customers.forEach(customer => {
            const customerElement = document.createElement('a');
            customerElement.href = '#';
            customerElement.className = 'list-group-item list-group-item-action select-customer-option';
            customerElement.dataset.customerId = customer.customerId;
            customerElement.dataset.customerName = customer.customerName;
            customerElement.dataset.customerPhone = customer.phoneNumber;
            // 將所有地址相關欄位存入 dataset
            customerElement.dataset.fullAddress = customer.fullAddress || '';
            customerElement.dataset.addressCityName = customer.addressCityName || '';
            customerElement.dataset.addressDistrictName = customer.addressDistrictName || '';
            customerElement.dataset.addressStreetName = customer.addressStreetName || '';
            customerElement.dataset.addressLane = customer.addressLane || '';
            customerElement.dataset.addressAlley = customer.addressAlley || '';
            customerElement.dataset.addressNumber = customer.addressNumber || '';
            customerElement.dataset.addressFloor = customer.addressFloor || '';
            customerElement.dataset.addressUnit = customer.addressUnit || '';

            customerElement.innerHTML = `<strong>${customer.customerName}</strong><br><small>${customer.phoneNumber} - ${customer.fullAddress || '無地址'}</small>`;
            customerSelectModalBody.appendChild(customerElement);
        });
    }

    /**
     * Handles the click event when a customer is selected from the modal.
     */
    function handleCustomerSelectionFromModal(event) {
        event.preventDefault();
        const target = event.target.closest('.select-customer-option');
        if (!target) return;

        // 從 data attributes 中獲取完整的客戶資料
        const customer = {
            customerId: target.dataset.customerId,
            customerName: target.dataset.customerName,
            phoneNumber: target.dataset.customerPhone,
            fullAddress: target.dataset.fullAddress,
            addressCityName: target.dataset.addressCityName,
            addressDistrictName: target.dataset.addressDistrictName,
            addressStreetName: target.dataset.addressStreetName,
            addressLane: target.dataset.addressLane,
            addressAlley: target.dataset.addressAlley,
            addressNumber: target.dataset.addressNumber,
            addressFloor: target.dataset.addressFloor,
            addressUnit: target.dataset.addressUnit
        };
        populateCustomerInfo(customer);

        const modalInstance = bootstrap.Modal.getInstance(document.getElementById('customerSelectModal'));
        if(modalInstance) modalInstance.hide();
    }

    /**
     * Populates the customer info fields on the main form.
     */
    function populateCustomerInfo(customer) {
        if (!customer) return;

        // Populate Customer Info Section
        document.getElementById('customerId').value = customer.customerId || '';
        document.getElementById('customerName').value = customer.customerName || '';
        document.getElementById('customerPhoneDisplay').value = customer.phoneNumber || '';
        document.getElementById('customerPhoneSearch').value = customer.phoneNumber || '';

        // Also populate Dispatch Info Section
        document.getElementById('contactName').value = customer.customerName || '';
        document.getElementById('contactPhone').value = customer.phoneNumber || '';

        // --- START: Address Population Logic ---
        const fullAddressInput = document.getElementById('installationAddress');
        if (fullAddressInput) {
            fullAddressInput.value = customer.fullAddress || '';
        }

        if (installationAddressComponent) {
            // 從客戶資料中準備地址元件需要的資料結構
            const addressData = {
                addressCityName: customer.addressCityName,
                addressDistrictName: customer.addressDistrictName,
                addressStreetName: customer.addressStreetName,
                addressLane: customer.addressLane,
                addressAlley: customer.addressAlley,
                addressNumber: customer.addressNumber,
                addressFloor: customer.addressFloor,
                addressUnit: customer.addressUnit,
            };
            // 呼叫地址元件的 setData 方法來更新其所有欄位
            installationAddressComponent.setData(addressData);
        }
        // --- END: Address Population Logic ---
    }

    /**
     * Builds the payload object for the API request from form data.
     * @returns {object} The order payload.
     */
    function buildPayload() {
        const itemsPayload = [];
        document.querySelectorAll('.tab-pane').forEach(pane => {
            if (pane.dataset.isPlaceholder === 'true') return;

            const itemGroupsForThisItem = [];
            const subItemRows = pane.querySelectorAll('.sub-items-table tbody tr');
            subItemRows.forEach(row => {
                const itemTypeCode = row.dataset.itemType === 'GIFT' ? 1 : 2;
                const salePrice = parseFloat(row.querySelector('.sale-price')?.textContent.replace(/,/g, '')) || 0;
                const listPrice = parseFloat(row.querySelector('.list-price')?.textContent.replace(/,/g, '')) || 0;
                const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 1;

                const group = {
                    orderItemGroupId: row.dataset.groupId || null,
                    productBarcode: row.dataset.barcode,
                    productName: row.cells[0].textContent,
                    warehouseId: row.querySelector('.warehouse-select')?.value || null,
                    requiresDispatch: row.querySelector('.dispatch-type-select')?.value === '1' ? 1 : 0,
                    awaitingMaterials: null, // This is now handled on backend based on isAwait
                    itemTypeCode: itemTypeCode,
                    quantity: quantity,
                    unitPrice: salePrice,
                    listPrice: listPrice,
                    isAwait: row.querySelector('.awaiting-material-btn').classList.contains('btn-dark') ? 1 : 0
                };
                itemGroupsForThisItem.push(group);
            });

            const originalGiftTotal = parseFloat(pane.dataset.originalGiftTotal) || 0;
            let exchangedGiftTotal = 0;
            pane.querySelectorAll('.gifts-table tbody tr').forEach(row => {
                const price = parseFloat(row.querySelector('.sale-price')?.textContent.replace(/,/g, '')) || 0;
                const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 0;
                exchangedGiftTotal += price * quantity;
            });

            const mainItem = {
                orderItemId: pane.dataset.mainOrderItemId === 'undefined' ? null : (pane.dataset.mainOrderItemId || null),
                productBarcode: pane.dataset.mainBarcode,
                productName: pane.dataset.mainProductName,
                mahjongTableSerialNumber: pane.querySelector('.serial-number')?.value || null,
                warehouseCode: pane.querySelector('.warehouse-select')?.value || null,
                requiresDispatch: parseInt(pane.querySelector('.dispatch-type-select').value, 10),
                quantity: 1, // Main product quantity is always 1
                unitPrice: parseFloat(pane.querySelector('.sale-price-display').value.replace(/,/g, '')) || 0,
                listPrice: parseFloat(pane.querySelector('.list-price-display').value.replace(/,/g, '')) || 0,
                itemTypeCode: 0,
                itemGroups: [],
                originalGiftTotal: parseFloat(pane.querySelector('.original-gift-total')?.textContent.replace(/,/g, '')) || 0,
                exchangedGiftTotal: parseFloat(pane.querySelector('.exchanged-gift-total')?.textContent.replace(/,/g, '')) || 0
            };

            // Collect item groups (gifts and addons) directly from the DOM
            pane.querySelectorAll('.sub-items-table tbody tr').forEach(row => {
                const salePriceSpan = row.querySelector('.sale-price');
                const groupUnitPrice = salePriceSpan ? parseFloat(salePriceSpan.textContent.replace(/,/g, '')) : 0;
                const listPriceSpan = row.querySelector('.list-price');
                const originalPrice = listPriceSpan ? parseFloat(listPriceSpan.textContent.replace(/,/g, '')) : 0;

                const group = {
                    orderItemGroupId: row.dataset.groupId || null,
                    productBarcode: row.dataset.barcode,
                    productName: row.querySelector('td:first-child').textContent,
                    itemTypeCode: row.dataset.itemType === 'GIFT' ? 1 : 2,
                    quantity: parseInt(row.querySelector('.quantity-input').value, 10),
                    unitPrice: groupUnitPrice,
                    listPrice: originalPrice,
                    requiresDispatch: parseInt(row.querySelector('.dispatch-type-select').value, 10),
                    warehouseId: row.querySelector('.warehouse-select').value || null,
                    isAwait: row.querySelector('.awaiting-material-btn').classList.contains('btn-dark') ? 1 : 0
                };
                mainItem.itemGroups.push(group);
            });

            itemsPayload.push(mainItem);
        });

        const payload = {
            orderId: currentOrderId,
            companyDivisionCode: window.getCompanyDivisionCode(), // Always get from context, not disabled select
            storeId: storeSelect.value || null,
            distributorId: distributorSelect.value || null,
            orderDate: orderDateInput.value ? new Date(orderDateInput.value).toISOString() : null,

            memberId: document.getElementById('customerId').value || null,
            customerName: document.getElementById('customerName').value,
            customerPhone: document.getElementById('customerPhoneDisplay').value,

            contactName: document.getElementById('contactName').value,
            contactPhone: document.getElementById('contactPhone').value,

            installationAddress: installationAddressComponent.getData().fullAddress,
            installationAddressCity: installationAddressComponent.getData().addressCityName,
            installationAddressDistrict: installationAddressComponent.getData().addressDistrictName,
            installationAddressStreet: installationAddressComponent.getData().addressStreetName,
            installationAddressLane: installationAddressComponent.getData().addressLane,
            installationAddressAlley: installationAddressComponent.getData().addressAlley,
            installationAddressNumber: installationAddressComponent.getData().addressNumber,
            installationAddressFloor: installationAddressComponent.getData().addressFloor,
            installationAddressUnit: installationAddressComponent.getData().addressUnit,

            installationDate: document.getElementById('installationDate').value || null,
            installationTimeSlot: document.getElementById('installationTimeSlot').value || null,
            technicianId: document.getElementById('technicianId').value || null,
            expectedCompletionDate: document.getElementById('expectedCompletionDate').value || null,
            dispatchNotes: document.getElementById('dispatchNotes').value,

            items: itemsPayload,

            paymentDetails: Array.from(document.querySelectorAll('.payment-item')).map(row => {
                const paymentMethodCode = row.dataset.type.toUpperCase();
                const amount = parseFloat(row.querySelector('.payment-amount')?.value);
                const cardInfoDisplay = row.querySelector('.card-info-display');
                let cardBrand = null;
                let cardIssuer = null;
                if (cardInfoDisplay && cardInfoDisplay.textContent && cardInfoDisplay.textContent.includes(' - ')) {
                    [cardBrand, cardIssuer] = cardInfoDisplay.textContent.split(' - ').map(s => s.trim());
                }

                return {
                    paymentMethodCode,
                    amount: amount || 0,
                    cardNumber: row.querySelector('.card-number')?.value || null,
                    cardInstallments: parseInt(row.querySelector('.card-installments')?.value) || 1,
                    bankName: row.querySelector('.bank-name')?.value || null,
                    remitterAccountLastFive: row.querySelector('.remitter-account')?.value || null,
                    bankAccountLastFive: row.querySelector('.bank-account')?.value || null,
                    cardBrand: cardBrand,
                    cardIssuer: cardIssuer,
                };
            }).filter(p => p.amount > 0),

            invoiceTypeCode: document.querySelector('input[name="invoiceTypeBtn"]:checked')?.value ? 
                             parseInt(document.querySelector('input[name="invoiceTypeBtn"]:checked').value) : null,
            taxIdNumber: document.getElementById('taxIdNumber')?.value || null,
            invoiceCompanyTitle: document.getElementById('invoiceCompanyTitle')?.value || null,
            invoiceDate: document.getElementById('invoiceDate')?.value || null,
            invoiceNumber: document.getElementById('invoiceNumber')?.value || null,

            storeInvoiceAmount: parseFloat(document.getElementById('storeInvoiceAmount').value) || 0,
            storeInvoiceCompanyTitle: document.getElementById('storeInvoiceCompanyTitle').value || null,
            storeTaxIdNumber: document.getElementById('storeTaxIdNumber').value || null,

            remarks: document.getElementById('orderRemarks')?.value,
            orderStatusCode: null,
            promotionId: promotionSelect.value || null,

            // Include taxType parameter
            taxType: document.querySelector('input[name="taxTypeBtn"]:checked')?.value === 'inclusive' ? 2 : 1,
        };
        return payload;
    }

    /**
     * Fetches and populates distributors based on the current company division.
     */
    async function loadDistributors() {
        if (!distributorSelect) return;
        const companyCode = window.getCompanyDivisionCode();
        try {
            const response = await fetchAuthenticated(`/api/v1/distributors?companyDivisionCode=${companyCode}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResponse = await response.json();
            // The distributor API returns data directly in an array within the 'data' property.
            // It is not a paginated response like others.
            if (!apiResponse.data) throw new Error("API response did not contain distributor data.");
            populateDropdown(distributorSelect, apiResponse.data, '請選擇銷售據點', 'distributorId', 'distributorName');
        } catch (error) {
            console.error('無法載入經銷商列表:', error);
            window.showToast?.('無法載入經銷商列表', 'danger');
            distributorSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fetches and populates salespersons based on the selected store or distributor.
     */
    async function loadSalespersons(storeId = null, distributorId = null) {
        let url = '/api/v1/users/salespersons?';
        if (storeId) {
            url += `storeId=${storeId}`;
        } else if (distributorId) {
            url += `distributorId=${distributorId}`;
        } else {
             populateDropdown(salespersonSelect, [], '請先選擇門市或據點', 'userAccountId', 'userName');
            return;
        }

        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API did not return salesperson data.");
            populateDropdown(salespersonSelect, apiResponse.data, '請選擇銷售人員', 'userAccountId', 'userName');
        } catch (error) {
            console.error('無法載入銷售人員列表:', error);
            if(window.showToast) showToast('無法載入銷售人員列表', 'danger');
            salespersonSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * 當門市選擇變更時，觸發倉庫列表的更新
     */
    async function handleStoreChange() {
        const storeId = storeSelect.value;
        await updateAvailableWarehouses(storeId);
        // 清空現有商品的倉庫選擇，以觸發重新選擇和驗證
        document.querySelectorAll('.warehouse-select').forEach(select => {
            select.innerHTML = '<option value="">請選擇倉庫</option>';
            // 獲取商品條碼來顯示庫存數量
            const row = select.closest('tr');
            const pane = select.closest('.tab-pane');
            let productBarcode = null;

            if (row && row.dataset.barcode) {
                // 這是附屬品的倉庫選擇
                productBarcode = row.dataset.barcode;
            } else if (pane && pane.dataset.mainBarcode) {
                // 這是主商品的倉庫選擇
                productBarcode = pane.dataset.mainBarcode;
            }

            populateWarehouseDropdown(select, null, productBarcode);
        });
        validateAllItemStock();
    }

    /**
     * 根據門市ID更新可用的倉庫列表
     * @param {string} storeId - The selected store's UUID.
     */
    async function updateAvailableWarehouses(storeId) {
        let url = '/api/v1/warehouses?';
        if (storeId) {
            const selectedStoreOption = storeSelect.querySelector(`option[value="${storeId}"]`);
            // 假設 store DTO 中包含 regionId，如果沒有，需要先根據 storeId 查詢
            // 暫時使用 /by-store/{storeId} 這個 API
            url = `/api/v1/warehouses/by-store/${storeId}`;
        } else {
            url += 'isMain=true';
        }

        try {
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('無法獲取倉庫列表');
            const result = await response.json();
            availableWarehouses = result.data || [];
        } catch (error) {
            console.error(error);
            showToast('載入倉庫列表失敗', 'danger');
            availableWarehouses = [];
        }
    }

    /**
     * 填充單個倉庫下拉選單，並顯示庫存數量
     * @param {HTMLElement} selectElement - The select element to populate.
     * @param {string} selectedValue - The pre-selected value.
     * @param {string} productBarcode - The product barcode to check stock for.
     */
    async function populateWarehouseDropdown(selectElement, selectedValue, productBarcode) {
        const hasSelectedOption = selectElement.querySelector('option[selected]');
        if(!hasSelectedOption) {
            selectElement.innerHTML = '<option value="">請選擇倉庫</option>';
        }

        // 如果沒有商品條碼，則不顯示庫存數量
        if (!productBarcode) {
            availableWarehouses.forEach(wh => {
                // Avoid adding a duplicate option if it's already there and selected
                if (selectElement.querySelector(`option[value="${wh.warehouseId}"]`)) return;
                const option = document.createElement('option');
                option.value = wh.warehouseId;
                option.textContent = wh.warehouseName;
                selectElement.appendChild(option);
            });
            if (selectedValue) {
                selectElement.value = selectedValue;
            }
            return;
        }

        // 並行查詢所有倉庫的庫存數量
        const stockPromises = availableWarehouses.map(async (wh) => {
            try {
                const response = await window.fetchAuthenticated(
                    `/api/v1/warehouse-inventory/stock?warehouseId=${wh.warehouseId}&productBarcode=${productBarcode}`
                );
                if (response.ok) {
                    const result = await response.json();
                    return { warehouse: wh, stock: result.data || 0 };
                } else {
                    return { warehouse: wh, stock: 0 };
                }
            } catch (error) {
                console.error(`查詢倉庫 ${wh.warehouseName} 庫存失敗:`, error);
                return { warehouse: wh, stock: 0 };
            }
        });

        try {
            const stockResults = await Promise.all(stockPromises);

            stockResults.forEach(({ warehouse, stock }) => {
                // Avoid adding a duplicate option if it's already there and selected
                if (selectElement.querySelector(`option[value="${warehouse.warehouseId}"]`)) return;
                const option = document.createElement('option');
                option.value = warehouse.warehouseId;
                option.textContent = `${warehouse.warehouseName} (庫存: ${stock})`;
                selectElement.appendChild(option);
            });

            if (selectedValue) {
                selectElement.value = selectedValue;
            }
        } catch (error) {
            console.error('查詢庫存數量失敗:', error);
            // 如果查詢失敗，則回退到不顯示庫存數量的方式
            availableWarehouses.forEach(wh => {
                if (selectElement.querySelector(`option[value="${wh.warehouseId}"]`)) return;
                const option = document.createElement('option');
                option.value = wh.warehouseId;
                option.textContent = wh.warehouseName;
                selectElement.appendChild(option);
            });
            if (selectedValue) {
                selectElement.value = selectedValue;
            }
        }
    }

    /**
     * 檢查指定行的庫存並顯示警告
     * @param {HTMLElement} warehouseSelectEl -觸發事件的倉庫下拉選單
     */
    async function checkStockAndWarn(warehouseSelectEl) {
        const row = warehouseSelectEl.closest('tr');
        const warningDiv = row.querySelector('.stock-warning');
        const quantity = parseInt(row.querySelector('.quantity-input').value);
        const productBarcode = row.dataset.barcode;
        const warehouseId = warehouseSelectEl.value;

        warningDiv.textContent = '';
        row.classList.remove('table-danger');

        if (!warehouseId || !productBarcode || !quantity) {
            validateAllItemStock(); // 確保在未選擇倉庫時按鈕是禁用的
            return;
        }

        try {
            const response = await window.fetchAuthenticated(`/api/v1/warehouse-inventory/stock?warehouseId=${warehouseId}&productBarcode=${productBarcode}`);
            if (!response.ok) {
                warningDiv.textContent = '查詢庫存失敗';
            } else {
                const result = await response.json();
                const stock = result.data;
                if (stock < quantity) {
                    warningDiv.textContent = `庫存不足 (僅剩 ${stock})`;
                    row.classList.add('table-danger');
                }
            }
        } catch (error) {
            console.error("Stock check failed:", error);
            warningDiv.textContent = '查詢庫存出錯';
        } finally {
            validateAllItemStock();
        }
    }

    /**
     * 驗證所有商品行的庫存狀態，並啟用/禁用提交按鈕
     * @returns {boolean} - 是否所有項目都驗證通過
     */
    function validateAllItemStock() {
        let allValid = true;
        document.querySelectorAll('.tab-pane').forEach(pane => {
            if (pane.dataset.isPlaceholder === 'true') {
                allValid = false;
                return;
            }
            
            // 檢查該主商品是否需要倉庫驗證
            const needsWarehouseValidation = shouldValidateWarehouseForPane(pane);
            
            if (needsWarehouseValidation) {
                // Check main product's warehouse
                const mainWarehouseSelect = pane.querySelector('.warehouse-select');
                if (!mainWarehouseSelect || !mainWarehouseSelect.value) {
                    mainWarehouseSelect?.classList.add('is-invalid');
                    allValid = false;
                } else {
                    mainWarehouseSelect?.classList.remove('is-invalid');
                }

                // Check sub-items' warehouses
                pane.querySelectorAll('.sub-items-table .warehouse-select').forEach(subSelect => {
                    if (!subSelect.value) {
                        subSelect.classList.add('is-invalid');
                        allValid = false;
                    } else {
                        subSelect.classList.remove('is-invalid');
                    }
                });
            } else {
                // 店取或自載展示機，移除倉庫驗證的錯誤狀態
                const mainWarehouseSelect = pane.querySelector('.warehouse-select');
                mainWarehouseSelect?.classList.remove('is-invalid');
                pane.querySelectorAll('.sub-items-table .warehouse-select').forEach(subSelect => {
                    subSelect.classList.remove('is-invalid');
                });
            }
        });
        return allValid;
    }

    function handleRemoveTab(tabId) {
        const tabLink = document.getElementById(tabId);
        if (!tabLink) return;

        const tabListItem = tabLink.closest('li');
        const paneId = tabLink.getAttribute('data-bs-target');
        const pane = document.querySelector(paneId);

        const allTabs = document.querySelectorAll('#dispatch-group-tabs .nav-item');

        if (allTabs.length > 1) {
            // 先移除 DOM 元素
            tabListItem.remove();
            if (pane) {
                pane.remove();
            }

            // 然後將剩餘的第一個 TAB 設為 active
            const remainingTabsContainer = document.getElementById('dispatch-group-tabs');
            const firstTabLink = remainingTabsContainer.querySelector('.nav-link');
            if (firstTabLink) {
                const tabToActivate = new bootstrap.Tab(firstTabLink);
                tabToActivate.show();
            }
        } else {
            // 只剩下最後一個TAB，則清空內容
            resetTabToPlaceholder(pane);
        }
        updateTotalAmountsUI();
    }

    function resetTabToPlaceholder(pane) {
        if (!pane) return;

        const paneId = pane.id;
        const tabId = pane.getAttribute('aria-labelledby');
        const tabCounterIndex = tabId.split('-').pop();

        // 1. Reset Tab Title
        const tabButton = document.getElementById(tabId);
        if (tabButton) {
            const titleSpan = tabButton.querySelector('span');
            if (titleSpan) {
                titleSpan.textContent = `訂單 ${tabCounterIndex}`;
            }
        }

        // 2. Reset Pane Data Attributes
        pane.dataset.mainBarcode = '';
        pane.dataset.isPlaceholder = 'true';
        pane.dataset.mainProductName = '待選擇...';
        pane.dataset.originalGiftTotal = '0';

        // 3. Clear Pane Input Fields
        pane.querySelector('.main-product-name-display').value = '待選擇...';
        pane.querySelector('.list-price-display').value = '0';
        pane.querySelector('.sale-price-display').value = '0';
        pane.querySelector('.amount-display').value = '0';
        pane.querySelector('.serial-number').value = '';
        const warehouseSelect = pane.querySelector('.warehouse-select');
        if (warehouseSelect) {
            const productBarcode = pane.dataset.mainBarcode;
            populateWarehouseDropdown(warehouseSelect, '', productBarcode); // Reset dropdown
        }

        // 4. Clear Sub-item Tables
        const giftsTableBody = pane.querySelector('.gifts-table tbody');
        if (giftsTableBody) giftsTableBody.innerHTML = '';
        const addonsTableBody = pane.querySelector('.addons-table tbody');
        if (addonsTableBody) addonsTableBody.innerHTML = '';

        // 5. Update gift section state (disable button, reset totals)
        updateGiftSectionState(pane);

        // 6. Update total calculation for the whole order
        updateTotalAmountsUI();
    }

    /**
     * Sets the form to a partial read-only state, allowing only warehouse selection.
     * @param {boolean} isReadOnly - True to make form mostly read-only, false to make it editable.
     */
    function setFormReadOnly(isReadOnly) {
        const fieldsToToggle = [
            '#storeSelect', '#distributorSelect', '#salespersonSelect', '#technicianId',
            '#customerPhoneSearch', '#searchCustomerBtn', 
            '#installation-address-component-placeholder input', 
            '#installation-address-component-placeholder select',
            '#contactName', '#contactPhone', '#installationDate', '#installationTimeSlot',
            '#expectedCompletionDate', '#dispatchNotes', '#orderRemarks',
            '#productKeyword', '#dispatch-product-search-btn'
        ];

        const invoiceAndPaymentFields = [
            '#invoice-details-container input', '#invoice-details-container select',
            'input[name="invoiceTypeBtn"]', 'input[name="taxTypeBtn"]',
            '#add-payment-buttons button', '#payment-methods-container input', 
            '#payment-methods-container select'
        ];

        // The condition is now >= 30
        const allowInvoiceAndPaymentEdit = currentOrderData && currentOrderData.orderStatusCode >= 30;

        document.querySelectorAll(fieldsToToggle.join(', ')).forEach(el => {
            el.disabled = isReadOnly;
        });

        // Loop through all dynamic elements like item rows and disable them too
        document.querySelectorAll('#dispatch-group-tab-content .form-control, #dispatch-group-tab-content .form-select, #dispatch-group-tab-content .btn-sm').forEach(el => {
            el.disabled = isReadOnly;
        });

        // Handle the special case for invoice and payment
        document.querySelectorAll(invoiceAndPaymentFields.join(', ')).forEach(el => {
            if (allowInvoiceAndPaymentEdit) {
                el.disabled = false; // Always enable if status is >= 30
            } else {
                el.disabled = isReadOnly;
            }
        });

        // Add a listener for when a tab is shown to update its gift section state
        const tabEl = document.querySelectorAll('a[data-bs-toggle="tab"]')
        tabEl.forEach(tab => {
            tab.addEventListener('shown.bs.tab', event => {
                const targetPaneId = event.target.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetPaneId);
                if (targetPane) {
                    updateGiftSectionState(targetPane);
                }
            });
        });
    }

    /**
     * Fetches and populates the technicians dropdown.
     */
    async function loadTechnicians() {
        const selectElement = document.getElementById('technicianId');
        if (!selectElement) return;

        try {
            const response = await window.fetchAuthenticated('/api/v1/users/technicians');
            if (!response.ok) throw new Error('無法載入技師列表');

            const apiResponse = await response.json();
            if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) {
                populateDropdown(selectElement, apiResponse.data, '請選擇指派技師', 'userAccountId', 'userName');
            } else {
                selectElement.innerHTML = '<option value="">無法載入技師</option>';
            }
        } catch (error) {
            console.error('無法載入技師列表:', error);
            if(window.showToast) showToast('無法載入技師列表', 'danger');
            selectElement.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    // --- EXECUTION ---
    initDispatchOrderForm();

    // --- New Action Handler ---
    async function handleHqAction(action, requiresReason = false) {
        let reason = null;
        if (requiresReason) {
            reason = prompt(`請輸入「${action === 'hq-reject' ? '駁回' : '退回'}」的原因：`);
            if (reason === null) return; // User cancelled prompt
            if (!reason.trim()) {
                showToast('必須提供原因', 'warning');
                return;
            }
        }

        try {
            const url = `/api/v1/orders/${currentOrderId}/${action}`;
            const payload = {
                method: 'POST',
                body: requiresReason ? JSON.stringify({ reason: reason }) : null,
            };
            const response = await fetchAuthenticated(url, payload);

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }
            showToast('操作成功', 'success');
            setTimeout(() => location.reload(), 1000);
        } catch (error) {
            console.error(`HQ action (${action}) failed:`, error);
            showToast(`操作失敗: ${error.message}`, 'danger');
        }
    }

    // --- New Action Handler for creating dispatch repair ---
    async function handleCreateDispatchRepair() {
        if (!currentOrderId) return;

        if (!confirm('您確定要為此訂單建立派工單嗎？')) return;

        try {
            const url = `/api/v1/dispatch-orders/from-order/${currentOrderId}`;
            const response = await fetchAuthenticated(url, { method: 'POST' });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '建立派工單失敗');
            }
            showToast('派工單已成功建立！', 'success');
            // Instead of reloading, maybe redirect to the dispatch list?
            setTimeout(() => {
                window.location.href = 'dispatch_tech_list.html';
            }, 1500);
        } catch (error) {
            console.error('Failed to create dispatch repair:', error);
            showToast(`建立派工單失敗: ${error.message}`, 'danger');
        }
    }

    /**
     * Handles the click event for the 'awaiting material' button.
     * @param {HTMLElement} button - The button that was clicked.
     */
    function handleAwaitingMaterialClick(button) {
        if (!button) return;
        const isCurrentlyAwaiting = button.classList.contains('btn-dark');
        const newAwaitingState = !isCurrentlyAwaiting;

        button.classList.toggle('btn-dark', newAwaitingState);
        button.classList.toggle('btn-outline-secondary', !newAwaitingState);

        // Optional: Update a data attribute on the row for clarity, though buildPayload uses the class.
        const row = button.closest('tr');
        if (row) {
            row.dataset.isAwait = newAwaitingState ? '1' : '0';
        }
    }

    function applyPromotionsToItems() {
        const selectedPromotionId = promotionSelect.value;
        const selectedPromotion = currentPromotionsData.find(p => p.promotionId === selectedPromotionId);

        document.querySelectorAll('.tab-pane').forEach(pane => {
            const mainProductBarcode = pane.dataset.mainBarcode;
            const salePriceDisplay = pane.querySelector('.sale-price-display');
            const listPriceDisplay = pane.querySelector('.list-price-display');

            if (mainProductBarcode && salePriceDisplay && listPriceDisplay) {
                let originalPrice = parseFloat(listPriceDisplay.value.replace(/,/g, ''));
                let finalPrice = originalPrice;

                if (selectedPromotion && selectedPromotion.products) {
                    const promoProduct = selectedPromotion.products.find(p => p.productBarcode === mainProductBarcode);
                    if (promoProduct) {
                        finalPrice = promoProduct.promoPrice;
                    }
                }

                salePriceDisplay.value = finalPrice.toLocaleString();
                if (finalPrice < originalPrice) {
                    salePriceDisplay.classList.add('text-success', 'fw-bold');
                } else {
                    salePriceDisplay.classList.remove('text-success', 'fw-bold');
                }
            }

            // Also update addons in the current pane
            pane.querySelectorAll('.addons-table tbody tr').forEach(row => {
                const addonBarcode = row.dataset.barcode;
                const salePriceSpan = row.querySelector('.sale-price');
                const originalPrice = parseFloat(row.querySelector('.list-price').textContent.replace(/,/g, ''));

                let finalAddonPrice = originalPrice;
                if (selectedPromotion && selectedPromotion.products) {
                    const promoProduct = selectedPromotion.products.find(p => p.productBarcode === addonBarcode);
                    if (promoProduct) {
                        finalAddonPrice = promoProduct.promoPrice;
                    }
                }
                salePriceSpan.textContent = finalAddonPrice.toLocaleString();

                if (finalAddonPrice < originalPrice) {
                    salePriceSpan.classList.add('text-success', 'fw-bold');
                } else {
                    salePriceSpan.classList.remove('text-success', 'fw-bold');
                }

                // Also update subtotal
                const quantity = parseInt(row.querySelector('.quantity-input').value, 10);
                row.querySelector('.item-subtotal').textContent = (finalAddonPrice * quantity).toLocaleString();
            });
        });
        updateTotalAmountsUI();
    }

    async function handleStoreOrDistributorChange() {
        const storeId = storeSelect.value;
        const distributorId = distributorSelect.value;
        // When store changes, update salespersons and available warehouses.
        if(storeId) {
            await Promise.all([
                loadSalespersons(storeId, null),
                updateAvailableWarehouses(storeId)
            ]);
        } else {
             await loadSalespersons(null, distributorId);
             availableWarehouses = []; // Clear warehouses if no store is selected
        }
        await loadPromotions(storeId, distributorId);
        applyPromotionsToItems();
        // After warehouses are loaded, refresh all dropdowns
        document.querySelectorAll('.warehouse-select').forEach(select => {
            // 獲取商品條碼來顯示庫存數量
            const row = select.closest('tr');
            const pane = select.closest('.tab-pane');
            let productBarcode = null;

            if (row && row.dataset.barcode) {
                // 這是附屬品的倉庫選擇
                productBarcode = row.dataset.barcode;
            } else if (pane && pane.dataset.mainBarcode) {
                // 這是主商品的倉庫選擇
                productBarcode = pane.dataset.mainBarcode;
            }

            populateWarehouseDropdown(select, null, productBarcode);
        });
    }

    async function updateAvailableWarehouses(storeId) {
        if (!storeId) {
            availableWarehouses = [];
            return;
        }
        try {
            const url = `/api/v1/warehouses/by-store/${storeId}`;
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('無法獲取倉庫列表');
            const result = await response.json();
            availableWarehouses = result.data || [];
        } catch (error) {
            console.error(error);
            showToast('載入倉庫列表失敗', 'danger');
            availableWarehouses = [];
        }
    }

    // --- 日期驗證相關函數 ---

    /**
     * 初始化日期驗證
     */
    function initializeDateValidation() {
        if (!installationDateInput) return;

        // 設置最小日期為今天
        setMinDateToToday();

        // 添加日期變更事件監聽器
        installationDateInput.addEventListener('change', validateInstallationDate);
        installationDateInput.addEventListener('blur', validateInstallationDate);
    }

    /**
     * 設置日期輸入的最小值為今天
     */
    function setMinDateToToday() {
        if (!installationDateInput) return;

        const today = new Date();
        const todayString = today.toISOString().split('T')[0];
        installationDateInput.setAttribute('min', todayString);
    }

    /**
     * 驗證安裝日期
     */
    function validateInstallationDate() {
        if (!installationDateInput) return true;

        const selectedDate = installationDateInput.value;
        const errorElement = document.getElementById('installationDateError');

        // 如果沒有選擇日期，清除錯誤狀態
        if (!selectedDate) {
            clearDateValidationError();
            return true;
        }

        const today = new Date();
        const selected = new Date(selectedDate);

        // 設置時間為當天開始，避免時區問題
        today.setHours(0, 0, 0, 0);
        selected.setHours(0, 0, 0, 0);

        if (selected < today) {
            showDateValidationError();
            return false;
        } else {
            clearDateValidationError();
            return true;
        }
    }

    /**
     * 顯示日期驗證錯誤
     */
    function showDateValidationError() {
        if (!installationDateInput) return;

        installationDateInput.classList.add('is-invalid');
        const errorElement = document.getElementById('installationDateError');
        if (errorElement) {
            errorElement.style.display = 'block';
        }
    }

    /**
     * 清除日期驗證錯誤
     */
    function clearDateValidationError() {
        if (!installationDateInput) return;

        installationDateInput.classList.remove('is-invalid');
        const errorElement = document.getElementById('installationDateError');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * 改善錯誤訊息處理
     */
    function parseErrorMessage(errorMessage) {
        // 如果是日期相關的錯誤，返回友好的訊息
        if (errorMessage && errorMessage.includes('installationDate')) {
            return '預計安裝日期不能選擇今天以前的日期';
        }

        // 如果是未知錯誤，嘗試提供更有用的訊息
        if (errorMessage === '未知錯誤' || !errorMessage) {
            return '操作失敗，請檢查輸入資料是否正確';
        }

        return errorMessage;
    }
});
